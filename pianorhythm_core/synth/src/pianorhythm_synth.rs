use std::collections::HashMap;

// External crates
use derivative::Derivative;
use lowpass_filter::lowpass_filter;
use midir::{MidiInput, MidiOutput};
use midly::live::LiveEvent;
use midly::MidiMessage;
use num_traits::{clamp, ToPrimitive};
use protobuf::Message;
use reactive_state::middleware::{Middleware, NotifyFn};
use reactive_state::{CompositeReducer, Store};
use rustc_hash::{FxHashMap, FxHashSet};

// Conditional WASM imports
#[cfg(target_arch = "wasm32")]
use js_sys::Array;
use oxisynth::{InterpolationMethod, MidiEvent, DEFAULT_PERCUSSION_KIT_BANK, DRUM_CHANNEL, MAX_MIDI_CHANNEL};
use pianorhythm_proto::midi_renditions::{ActiveChannelsMode, AudioChannel, SetChannelInstrumentPayload, SetChannelInstrumentType};
use pianorhythm_proto::pianorhythm_actions::AppStateActions;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects;
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_shared::*;
use simple_eq::*;
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::{prelude::*, JsValue};
#[cfg(target_arch = "wasm32")]
use wasm_msgpack::*;
#[cfg(target_arch = "wasm32")]
use web_sys::{AudioContext, AudioWorkletNode, BroadcastChannel};

use crate::isynthesizers::oxisynth::OxiSynth;
use crate::state::{ClientSynthState, ClientSynthStateAction, ClientSynthStateStateReducer};
use crate::types::*;
#[cfg(target_arch = "wasm32")]
use crate::wasm_audio::wasm_audio_node;
use crate::{ISynthesizer, SYNTH};

use super::utils::Range;

const DRUM_PROGRAMS: [u8; 10] = [1, 9, 17, 25, 26, 33, 41, 49, 57, 128];
const TRANSPOSE_OFFSET_RANGE: Range<i8> = Range { min: -14, max: 14 };
const MULTI_MODE_MAX_CHANNEL_RANGE: Range<u8> = Range { min: 1, max: 16 };
const OCTAVE_OFFSET_RANGE: Range<i8> = Range { min: -7, max: 7 };
const MIN_MIDI_VELOCITY: u8 = 0;
const MAX_MIDI_VELOCITY: u8 = 127;
const MIDI_CONTROL_CHANGE: u8 = 176;
const MIDI_RELATED_NOTE_SOURCES: [NoteSourceType; 2] = [NoteSourceType::Midi, NoteSourceType::MidiPlayer];

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
extern "C" {
    #[cfg(feature = "stand_alone")]
    fn onBroadcastSynthEvent(value: &JsValue);
    #[cfg(feature = "stand_alone")]
    fn postSynthEmitMessage(value: &JsValue);
    #[wasm_bindgen(js_namespace = console)]
    fn log(s: &str);
}

type OnMidiSynthEventFn<'a> = &'a mut dyn FnMut(&PianoRhythmSynthEvent);

pub struct PianoRhythmSynthesizer {
    instrument_channels: HashMap<u8, SynthInstrumentChannel>,
    equalizer: Equalizer<f32>,
    synth: Box<dyn ISynthesizer>,
    client_socket_id: Option<u32>,

    loaded_banks: Vec<u32>,
    loaded_presets: Vec<u32>,
    octave_offset: i8,
    transpose_offset: i8,
    disable_velocity_for_client: bool,
    pub apply_velocity_curve: bool,
    use_default_instrument_when_missing_for_other_users: bool,
    midi_auto_fill_empty_channels_with_default_instrument: bool,
    pub equalizer_enabled: bool,
    slot_mode: ActiveChannelsMode,
    split_keys: Vec<Vec<u8>>,
    primary_channel: u8,
    max_velocity: Option<u8>,
    min_velocity: Option<u8>,
    max_multi_mode_channels: u8,
    global_user_velocity: u32,
    user_velocity_percentages: FxHashMap<u32, u32>,
    user_volumes: FxHashMap<u32, u32>,
    drum_channel_muted: bool,
    midi_output_only: bool,

    sample_rate: f32,
    use_separate_drum_kit: bool,
    listen_to_program_changes: bool,
    use_default_bank_when_missing: bool,
    is_drum_channel_muted: bool,

    #[cfg(feature = "stand_alone")]
    #[cfg(target_arch = "wasm32")]
    emit_to_websocket_broadcast_channel: BroadcastChannel,
    pub emit_to_websocket_cb: Option<WebsocketEmitEventCallback>,
    pub synth_events_cb: Option<PianoRhythmSynthEventCallback>,
    pub audio_channel_update_cb: Option<PianoRhythmAudioChannelUpdateCallback>,
}

impl PianoRhythmSynthesizer {
    pub fn read_next(&mut self) -> (f32, f32) {
        if self.has_soundfont_loaded() {
            self.synth.read_next()
        } else {
            (0.0, 0.0)
        }
    }

    fn send_event(&mut self, event: oxisynth::MidiEvent, socket_id: &Option<u32>) {
        if self.has_soundfont_loaded() {
            // log::info!("send_event: {:?} | {:?}", &event, &socket_id);

            if let Some(ref id) = self.try_get_client_socket_id(socket_id) {
                if !self.has_socket_id(id) {
                    // log::info!("synth doesn't have socket id: {:?}", id);
                    return;
                }

                // log::info!("send_event: {:?} | {:?}", &event, &id);

                match self.synth.send_event(event, id) {
                    Err(err) => {
                        // log::error!("send_event error: {}", err);
                        #[cfg(target_arch = "wasm32")]
                        log::error!("send_event error: {:?}", err);
                    }
                    _ => {}
                }
            }
        }
    }
}

impl PianoRhythmSynthesizer {
    pub fn new(
        options: PianoRhythmSynthesizerDescriptor, emit_to_websocket_cb: Option<WebsocketEmitEventCallback>,
        synth_events_cb: Option<PianoRhythmSynthEventCallback>, audio_channel_update_cb: Option<PianoRhythmAudioChannelUpdateCallback>,
    ) -> Self {
        let sanitized = options.clone().sanitize();
        let sample_rate = sanitized.sample_rate.unwrap_or(PianoRhythmSynthesizerDescriptor::DEFAULT_SAMPLE_RATE);

        let synth_descriptor = oxisynth::SynthDescriptor {
            reverb_active: true,
            chorus_active: true,
            min_note_length: 1,
            audio_channels: sanitized.audio_channels,
            sample_rate,
            gain: 1.0,
            ..Default::default()
        };

        let synth = match options.backend_synth {
            // PianoRhythmSynthesizerBackendSynth::RUSTYSYNTH => Box::new(RustySynth::new(synth_descriptor)) as Box<dyn ISynthesizer>,
            _ => Box::new(OxiSynth::new(synth_descriptor)) as Box<dyn ISynthesizer>,
        };

        let mut instrument_channels = HashMap::new();
        for i in 0..=MAX_MIDI_CHANNEL {
            instrument_channels.insert(i as u8, SynthInstrumentChannel::default());
        }

        Self {
            synth,
            sample_rate,
            instrument_channels,
            client_socket_id: None,
            equalizer: Equalizer::new(sample_rate),

            octave_offset: 0,
            transpose_offset: 0,
            user_velocity_percentages: FxHashMap::default(),
            user_volumes: FxHashMap::default(),
            disable_velocity_for_client: false,
            apply_velocity_curve: false,
            use_default_instrument_when_missing_for_other_users: true,
            midi_auto_fill_empty_channels_with_default_instrument: false,
            equalizer_enabled: false,
            slot_mode: ActiveChannelsMode::ALL,
            split_keys: vec![vec![0; 0]; 0],
            primary_channel: 0,
            drum_channel_muted: false,
            midi_output_only: false,
            max_velocity: None,
            min_velocity: None,
            global_user_velocity: pianorhythm_shared::audio::MAX_VELOCITY_USER_PERCENTAGE as u32,
            max_multi_mode_channels: pianorhythm_shared::defaults::AUDIO_MULTIMODE_MAX_CHANNELS,
            loaded_banks: vec![],
            loaded_presets: vec![],

            use_separate_drum_kit: false,
            listen_to_program_changes: false,
            use_default_bank_when_missing: false,
            is_drum_channel_muted: false,

            emit_to_websocket_cb,
            synth_events_cb,
            audio_channel_update_cb,

            #[cfg(feature = "stand_alone")]
            #[cfg(target_arch = "wasm32")]
            emit_to_websocket_broadcast_channel: BroadcastChannel::new("pianorhythm.synth.emit-socket.events").unwrap(),
        }
    }

    pub fn is_client(&self, socket_id: &Option<u32>) -> bool {
        if let Some(id) = socket_id {
            return self.client_socket_id.as_ref() == Some(id) || id.eq(&pianorhythm_shared::SYNTH_CONSTS::ID::MIDI_SYNTH_SOCKET_ID);
        }

        return socket_id.is_none();
    }

    pub fn is_client_midi_player(&self, socket_id: &Option<u32>) -> bool {
        if let Some(id) = socket_id {
            return id.eq(&pianorhythm_shared::SYNTH_CONSTS::ID::MIDI_SYNTH_SOCKET_ID);
        }

        return false;
    }

    /// Events that are to be broadcasted back to the outside in JS
    #[cfg(target_arch = "wasm32")]
    fn broadcast_event(&self, event: &PianoRhythmSynthEvent) {
        if !self.has_soundfont_loaded() {
            return ();
        }

        #[cfg(feature = "stand_alone")]
        {
            let mut msg_buf = [0u8; 500];
            let len = encode::serde::to_array(&event, &mut msg_buf).unwrap();
            let js_output = &JsValue::from(js_sys::Uint8Array::from(&msg_buf[..len]));
            onBroadcastSynthEvent(js_output);
        }
    }

    /// Events that are to be emitted through the websocket to the server
    fn broadcast_emit_event(&self, event: &PianoRhythmWebSocketEmitEvent) {
        if !self.has_soundfont_loaded() {
            return ();
        }

        if let Some(cb) = &self.emit_to_websocket_cb {
            cb(event.to_owned())
        }

        #[cfg(feature = "stand_alone")]
        {
            #[cfg(target_arch = "wasm32")]
            if let Ok(output) = serde_json::to_string::<PianoRhythmWebSocketEmitEvent>(event) {
                let js_output = &JsValue::from(output);

                match (self.emit_to_websocket_broadcast_channel.post_message(js_output)) {
                    Ok(_) => {}
                    Err(err) => {
                        postSynthEmitMessage(&js_output);
                    }
                }
            }
        }
    }

    fn list_midi_input_connections_raw(&self) -> HashMap<usize, String> {
        let midi_in = MidiInput::new("piano-trainer-input");
        match midi_in {
            Ok(midi_in) => {
                let mut midi_connections = HashMap::new();
                for (i, p) in midi_in.ports().iter().enumerate() {
                    let port_name = midi_in.port_name(p);
                    match port_name {
                        Ok(port_name) => {
                            midi_connections.insert(i, port_name);
                        }
                        Err(e) => {
                            log::error!("Error getting input port name: {}", e);
                        }
                    }
                }
                midi_connections
            }
            Err(_) => HashMap::new(),
        }
    }

    fn list_midi_output_connections_raw(&self) -> HashMap<usize, String> {
        let midi_out = MidiOutput::new("pianorhythm-output");
        match midi_out {
            Ok(midi_out) => {
                let mut midi_connections = HashMap::new();
                for (i, p) in midi_out.ports().iter().enumerate() {
                    let port_name = midi_out.port_name(p);

                    match port_name {
                        Ok(port_name) => {
                            midi_connections.insert(i, port_name);
                        }
                        Err(e) => {
                            log::error!("Error getting output port name: {}", e);
                        }
                    }
                }
                midi_connections
            }
            Err(_) => HashMap::new(),
        }
    }

    #[cfg(target_arch = "wasm32")]
    pub fn list_midi_input_connections(&mut self) -> JsValue {
        return serde_wasm_bindgen::to_value(&self.list_midi_input_connections_raw()).unwrap_or(JsValue::NULL);
    }

    #[cfg(not(target_arch = "wasm32"))]
    #[cfg(feature = "desktop_lib")]
    pub fn list_midi_input_connections(&mut self) -> HashMap<usize, String> {
        return self.list_midi_input_connections_raw();
    }

    #[cfg(target_arch = "wasm32")]
    pub fn list_midi_output_connections(&mut self) -> JsValue {
        return serde_wasm_bindgen::to_value(&self.list_midi_output_connections_raw()).unwrap_or(JsValue::NULL);
    }

    #[cfg(not(target_arch = "wasm32"))]
    #[cfg(feature = "desktop_lib")]
    pub fn list_midi_output_connections(&mut self) -> HashMap<usize, String> {
        return self.list_midi_output_connections_raw();
    }

    pub fn has_socket_id(&self, socket_id: &u32) -> bool {
        return self.synth.has_socket_id(socket_id);
    }

    pub fn set_client_socket_id(&mut self, socket_id: &u32) {
        self.client_socket_id = Some(*socket_id)
    }

    pub fn set_socket_volume(&mut self, socket_id: &u32, value: u32) {
        _ = self.user_volumes.insert(*socket_id, value);
    }

    pub fn set_socket_velocity_percentage(&mut self, socket_id: &u32, value: u32) {
        _ = self.user_velocity_percentages.insert(*socket_id, value);
    }

    pub fn add_socket(&mut self, socket_id: &u32, is_client: bool) -> bool {
        let added = self.synth.add_socket(socket_id, is_client);

        if added {
            self.set_socket_volume(socket_id, 100);
        }

        added
    }

    pub fn clear_all_users_except_client(&mut self) {
        for user in self.synth.get_user_ids() {
            if self.is_client(&Some(user)) {
                continue;
            }
            self.remove_socket(&user);
        }

        self.user_velocity_percentages.clear();
        self.user_volumes.clear();
    }

    pub fn remove_socket(&mut self, socket_id: &u32) {
        self.synth.remove_socket(socket_id);
        _ = self.user_volumes.remove(socket_id);
        _ = self.user_velocity_percentages.remove(socket_id);

        if self.is_client(&Some(*socket_id)) {
            self.broadcast_emit_event(&PianoRhythmWebSocketEmitEvent {
                system_reset: Some(true),
                ..PianoRhythmWebSocketEmitEvent::default()
            })
        }
    }

    pub fn mute_socket(&mut self, socket_id: &Option<u32>, value: bool) {
        if let Some(ref id) = socket_id {
            self.synth.mute_socket(id, value);
        }
    }

    fn get_synth_users_raw(&self) -> Vec<u32> {
        self.synth.get_user_ids()
    }

    #[cfg(target_arch = "wasm32")]
    pub fn get_synth_users(&mut self) -> Array {
        return self.get_synth_users_raw().iter().map(|x| JsValue::from(x.to_string())).collect();
    }

    #[cfg(not(target_arch = "wasm32"))]
    #[cfg(feature = "desktop_lib")]
    pub fn get_synth_users(&self) -> Vec<u32> {
        return self.get_synth_users_raw();
    }

    pub fn process(&mut self, output: &mut [f32]) {
        let mut chunks = output.chunks_exact_mut(2);
        for chunk in &mut chunks {
            let (mut l, mut r) = self.read_next();
            self.equalize(&mut l);
            self.equalize(&mut r);
            chunk[0] = l;
            chunk[1] = r;
        }

        // Handle remaining sample if output length is odd
        if let Some(remaining) = chunks.into_remainder().get_mut(0) {
            let (mut l, _) = self.read_next();
            self.equalize(&mut l);
            *remaining = l;
        }
    }

    pub fn process_stereo(&mut self, buffer_l: &mut [f32], buffer_r: &mut [f32]) {
        let len = buffer_l.len().min(buffer_r.len());

        for i in 0..len {
            let (mut l, mut r) = self.read_next();

            self.equalize(&mut l);
            self.equalize(&mut r);

            buffer_l[i] = l;
            buffer_r[i] = r;
        }
    }

    #[inline]
    pub fn equalize(&mut self, sample: &mut f32) {
        if (self.equalizer_enabled) {
            *sample = self.equalizer.process(*sample);
        }
    }

    #[inline]
    pub fn equalize_buffer(&mut self, sample: &mut [f32]) {
        if (self.equalizer_enabled) {
            self.equalizer.process_buffer(sample);
        }
    }

    pub fn get_equalizer_mut(&mut self) -> &mut Equalizer<f32> {
        return &mut self.equalizer;
    }

    pub fn get_all_presets_from_sf_raw(&self) -> Vec<pianorhythm_proto::midi_renditions::SoundfontPreset> {
        return self
            .synth
            .get_all_presets_from_sf_raw()
            .iter()
            .map(|p| {
                let mut preset = pianorhythm_proto::midi_renditions::SoundfontPreset::new();
                preset.set_name(p.name.clone());
                preset.set_bank(p.bank.clone());
                preset.set_preset(p.preset.clone());
                preset.set_key_low(p.key_low.clone() as u32);
                preset.set_key_high(p.key_high.clone() as u32);
                preset
            })
            .collect();
    }

    #[cfg(not(target_arch = "wasm32"))]
    #[cfg(feature = "desktop_lib")]
    pub fn get_all_presets_from_sf(&self) -> Vec<pianorhythm_proto::midi_renditions::SoundfontPreset> {
        return self.get_all_presets_from_sf_raw();
    }

    #[cfg(target_arch = "wasm32")]
    pub fn get_all_presets_from_sf(&self) -> Array {
        return self
            .get_all_presets_from_sf_raw()
            .iter()
            .map(|x| x.write_to_bytes().unwrap_or_default())
            .map(|bytes| js_sys::Uint8Array::from(&bytes[..]))
            .map(JsValue::from)
            .collect();
    }

    pub fn load_soundfont(&mut self, buf: &[u8]) -> Result<(), String> {
        return match self.synth.load_soundfont(buf) {
            Ok(_) => {
                let presets = self.get_all_presets_from_sf_raw();
                let iter = presets.iter();
                self.loaded_banks = iter.clone().map(|pre| pre.bank).collect();
                self.loaded_presets = iter.map(|pre| pre.preset).collect();

                // Reset all channels except first
                let client_socket_id = self.client_socket_id;
                for i in 0..=MAX_MIDI_CHANNEL {
                    let channel = i as u8;
                    if i == 0 {
                        self.set_channel_active(channel, true, &client_socket_id);
                    } else {
                        self.clear_program_on_channel(channel, &client_socket_id);
                    }
                }

                self.broadcast_all_client_channels_update();

                Ok(())
            }
            Err(err) => Err(err),
        };
    }

    pub fn has_soundfont_loaded(&self) -> bool {
        return self.synth.has_soundfont_loaded();
    }

    pub fn parse_midi_data_no_broadcast(
        &mut self, _data: &[u8], input_socket_id: &Option<u32>, source: Option<u8>, device_id: Option<u32>,
    ) -> Option<Vec<PianoRhythmSynthEvent>> {
        if !self.has_soundfont_loaded() || (_data.len() == 0) {
            return None;
        }

        let socket_id = &input_socket_id.or(self.client_socket_id);
        let is_client: bool = socket_id.is_none() || self.is_client(&socket_id);
        let mut data = _data.to_vec();

        if (data.len() > 3) {
            data.truncate(3);
        }

        let note_source = NoteSourceType::from_u8_option(source);

        return match midi_parse(&data) {
            None => None,
            Some((channel, message)) => {
                let mut is_note_related_event = false;
                let mut events: Vec<PianoRhythmSynthEvent> = Vec::new();

                let channels_to_loop: u8 =
                    // Don't loop if percussion channel
                    if (is_client && self.slot_mode == ActiveChannelsMode::MULTI && channel != DRUM_CHANNEL as u8) {
                        self.max_multi_mode_channels
                    } else {
                        1
                    };

                // Don't process percussion events if channel is muted
                if (channel == DRUM_CHANNEL as u8 && self.drum_channel_muted) {
                    return None;
                }

                // log::trace!("MIDI Message: {:?} | Channel: {:?} | Socket: {:?} | Device ID: {:?}", &message, &channel, &socket_id, &device_id);

                match message {
                    MidiMessage::NoteOn { key, vel } => {
                        for each_channel in 0..channels_to_loop {
                            let target_channel = self.get_target_channel_in_multi_mode(channel, each_channel);

                            if let Some(mut data) = self.note_on(target_channel, key.as_int(), vel.as_int(), socket_id, device_id, note_source) {
                                is_note_related_event = true;
                                events.append(&mut data);
                            };
                        }
                    }
                    MidiMessage::NoteOff { key, vel: _ } => {
                        for each_channel in 0..channels_to_loop {
                            if let Some(mut data) = self.note_off(
                                self.get_target_channel_in_multi_mode(channel, each_channel),
                                key.as_int(),
                                socket_id,
                                device_id,
                                note_source,
                            ) {
                                is_note_related_event = true;
                                events.append(&mut data);
                            }
                        }
                    }
                    MidiMessage::ProgramChange { program } => {
                        let mut program_number = program.as_int();
                        if (channel == DRUM_CHANNEL as u8) {
                            if (DRUM_PROGRAMS.contains(&program_number) && program_number > 0) {
                                program_number -= 1;
                            }
                            data[1] = program_number;
                        }
                        self.program_select(channel, program_number, socket_id);
                    }
                    MidiMessage::Controller { controller, value } => {
                        // log::trace!("Controller: {:?} | Value: {:?} | Socket: {:?} | {:?}", &controller, &value, &socket_id, &is_client);
                        if is_client && controller.as_int() == MIDI_CONTROL_BYTES::DAMPER_PEDAL {
                            for each_channel in 0..channels_to_loop {
                                let target_channel = self.get_target_channel_in_multi_mode(channel, each_channel);
                                self.control_change(target_channel, controller.as_int(), value.as_int(), socket_id, note_source);
                            }
                        } else {
                            self.control_change(channel, controller.as_int(), value.as_int(), socket_id, note_source);
                        }
                    }
                    MidiMessage::PitchBend { bend } => {
                        if let Some(synth_event) = self.pitch_bend(channel, (bend.as_int() + 0x1FFF as i16 + 1) as u16, socket_id) {
                            is_note_related_event = true;
                            events.push(synth_event);
                        }
                    }
                    MidiMessage::ChannelAftertouch { vel } => {
                        self.channel_pressure(channel, vel.as_int(), socket_id);
                    }
                    MidiMessage::Aftertouch { key, vel } => {
                        self.poly_key_pressure(channel, key.as_int(), vel.as_int(), socket_id);
                    }
                }

                // Output sending doesn't work in web mode for some reason.
                // for output in self.midi_state.outputs.lock().unwrap().values_mut() {
                //   output
                //     .send(data)
                //     .unwrap_or_else(|err| println!("Error when forwarding message... | Data: {:?} | Error: {}", data, err));
                // }

                if (!is_note_related_event) {
                    self.create_synth_event(message, data.as_slice(), channel, socket_id, source, device_id)
                        .inspect(|event| {
                            events.push(event.to_owned());
                        });
                }

                if events.len() > 0 {
                    return Some(events);
                }

                return None;
            }
        };
    }

    fn broadcast_audio_channel_update(&self, events: Vec<AudioChannel>) {
        if events.is_empty() {
            return;
        }

        // log::trace!("broadcast_audio_channel_update: {:?}", &events);
        if let Some(cb) = &self.audio_channel_update_cb {
            cb(events);
        }
    }

    fn broadcast_client_audio_channel_update(&self, channel: u8) {
        if let Some(_id) = self.client_socket_id {
            self.synth.get_audio_channel(channel, &_id).inspect(|channel| {
                self.broadcast_audio_channel_update(vec![channel.clone()]);
            });
        }
    }

    fn broadcast_all_client_channels_update(&self) {
        let audio_channels = self.get_all_audio_channels(&self.client_socket_id);
        if audio_channels.is_empty() {
            return;
        }

        self.broadcast_audio_channel_update(audio_channels);
    }

    fn broadcast_synth_events(&self, events: Vec<PianoRhythmSynthEvent>) {
        if events.is_empty() {
            return;
        }

        // log::debug!("[broadcast_synth_events] {:?}", &events);

        self.broadcast_audio_channel_update(events.iter().filter(|x| x.is_client).flat_map(|x| x.audio_channel.clone()).collect());

        if let Some(cb) = &self.synth_events_cb {
            cb(events)
        } else {
            #[cfg(target_arch = "wasm32")]
            events.iter().for_each(|synth_event| {
                // This will broadcast the event back to the client
                self.broadcast_event(synth_event);
            });
        }
    }

    pub fn parse_midi_data(
        &mut self, _data: &[u8], input_socket_id: &Option<u32>, source: Option<u8>, device_id: Option<u32>,
    ) -> Option<Vec<PianoRhythmSynthEvent>> {
        if _data.is_empty() {
            return None;
        }

        let output = self.parse_midi_data_no_broadcast(_data, &self.try_get_client_socket_id(input_socket_id), source, device_id);
        // log::debug!("PARSE_OUTPUT: {:?}", &output);

        if let Some(events) = &output {
            self.broadcast_synth_events(events.clone());
        }

        return output;
    }

    pub fn create_synth_event(
        &mut self, message: MidiMessage, data: &[u8], channel: u8, socket_id: &Option<u32>, source: Option<u8>, device_id: Option<u32>,
    ) -> Option<PianoRhythmSynthEvent> {
        if let Some(_id) = socket_id {
            if let Some(audio_channel) = self.synth.get_audio_channel(channel, _id) {
                let message_output = midi_message_to_event(channel, message);

                let mut raw_bytes = data.to_vec();
                if message_output.map(|x| x.is_program_change()).unwrap_or_default() && raw_bytes.len() == 3 {
                    raw_bytes.pop();
                }

                let is_channel_update_message = message_output.map(|x| x.is_channel_update_message()).unwrap_or_default();

                let synth_event = PianoRhythmSynthEvent {
                    message: message_output,
                    message_type: message_output.map(|x| x.to_message_type_u8()),
                    channel,
                    raw_bytes,
                    current_program: Some(audio_channel.get_preset() as u8),
                    current_bank: Some(audio_channel.get_bank() as i32),
                    current_volume: Some(audio_channel.get_volume() as u8),
                    current_pitch: Some(audio_channel.get_pitch_bend()),
                    current_expression: Some(audio_channel.get_expression() as u8),
                    current_pan: Some(audio_channel.get_pan() as u8),
                    device_id,
                    is_client: self.is_client(socket_id),
                    socket_id: socket_id.clone(),
                    audio_channel: if is_channel_update_message { Some(audio_channel) } else { None },
                    source,
                    ..Default::default()
                };

                return Some(synth_event);
            }
        }

        None
    }

    pub fn program_select(&mut self, channel: u8, program_id: u8, socket_id: &Option<u32>) {
        self.send_event(MidiEvent::ProgramChange { channel, program_id }, socket_id);

        if (self.is_client(socket_id)) {
            if let Some(synth_channel) = self.instrument_channels.get_mut(&channel) {
                synth_channel.instrument = Some(program_id);
            }
        }
    }

    pub fn synth_set_user_gain(&mut self, value: f32, socket_id: &Option<u32>) {
        if self.is_client(socket_id) {
            return;
        }

        if let Some(ref id) = socket_id {
            self.synth.set_socket_user_gain(id, value);
        }
    }

    pub fn instrument_exists(&self, banknum: u32, prognum: u8) -> bool {
        if !self.has_soundfont_loaded() {
            return false;
        }
        return self.synth.instrument_exists(banknum, prognum);
    }

    fn get_target_channel_in_multi_mode(&self, orig_channel: u8, multi_channel: u8) -> u8 {
        if (self.slot_mode == ActiveChannelsMode::MULTI && orig_channel != DRUM_CHANNEL as u8) {
            multi_channel
        } else {
            orig_channel
        }
    }

    fn get_modified_channel(&self, channel: u8, note: u8, source_type: NoteSourceType, socket_id: &Option<u32>) -> u8 {
        if (!self.is_client(socket_id)) {
            return channel;
        }

        // Percussion channel is excluded from logic
        if (channel == DRUM_CHANNEL as u8) {
            return channel;
        }

        // Single mode will only use the default channel (0)
        if (self.slot_mode == ActiveChannelsMode::SINGLE) {
            return 0;
        }

        let primary_channel = self.primary_channel;
        let mut modified_channel: u8 = channel;

        match self.slot_mode {
            ActiveChannelsMode::SINGLE => {
                modified_channel = channel;

                //Ensure channel for midi input is set to primary when incoming
                //channel is 0
                if (MIDI_RELATED_NOTE_SOURCES.contains(&source_type) && channel == 0) {
                    modified_channel = primary_channel;
                }
            }
            ActiveChannelsMode::MULTI => {
                modified_channel = channel;
            }
            ActiveChannelsMode::ALL => {
                if (MIDI_RELATED_NOTE_SOURCES.contains(&source_type) && channel == 0) {
                    modified_channel = primary_channel
                }

                //Ensure channel for mouse input is set to primary
                if (source_type == NoteSourceType::Mouse) {
                    modified_channel = primary_channel
                }
            }
            ActiveChannelsMode::SPLIT2 | ActiveChannelsMode::SPLIT4 | ActiveChannelsMode::SPLIT8 => {
                modified_channel = self.split_keys.iter().position(|r| r.contains(&note)).unwrap_or(channel as usize) as u8;
            }
            _ => {}
        }

        return modified_channel;
    }

    pub fn note_on(
        &mut self, channel: u8, _key: u8, vel: u8, socket_id: &Option<u32>, device_id: Option<u32>, source_type: NoteSourceType,
    ) -> Option<Vec<PianoRhythmSynthEvent>> {
        if !self.has_soundfont_loaded() {
            return None;
        }

        let mut events: Vec<PianoRhythmSynthEvent> = Vec::new();

        let mut modified_key: u8 = if (source_type == NoteSourceType::Ignored) {
            _key
        } else {
            (_key as i8).saturating_add(self.get_note_modifier()) as u8
        };

        let modified_channel = self.get_modified_channel(channel, modified_key, source_type, socket_id);

        let mut modified_vel = vel;

        if self.apply_velocity_curve {
            modified_vel = apply_velocity_curve(modified_vel);
        }

        if self.is_client(socket_id) {
            if self.disable_velocity_for_client {
                modified_vel = MAX_MIDI_VELOCITY;
            }

            //Handle auto populate instrument (except for Multi Mode)
            if self.midi_auto_fill_empty_channels_with_default_instrument
                && self.slot_mode != ActiveChannelsMode::MULTI
                && MIDI_RELATED_NOTE_SOURCES.contains(&source_type)
                && (!self.is_instrument_loaded_in_channel(modified_channel) || !self.is_channel_active(modified_channel, socket_id))
            {
                if let Some(default_instrument) = self.synth.get_default_instrument() {
                    let bank = default_instrument.bank.clone();
                    let program: u32 = default_instrument.preset.clone();
                    let current_program = self.get_program(modified_channel, socket_id);
                    self.set_channel_active(modified_channel, true, socket_id);

                    // Set Bank
                    if (current_program.map(|cp| cp.0) != Some(bank)) {
                        if let Some(mut output) = self.parse_midi_data_no_broadcast(
                            &[MIDI_CONTROL_CHANGE + modified_channel, 0, bank as u8],
                            socket_id,
                            Some(source_type as u8),
                            None,
                        ) {
                            events.append(&mut output);
                        };
                    }

                    // Set Program
                    let o_current_program = current_program.map(|cp| cp.1);
                    if (o_current_program != Some(program as u8) || o_current_program == Some(0)) {
                        if let Some(mut output) =
                            self.parse_midi_data_no_broadcast(&[192 + modified_channel, program as u8, 0], socket_id, Some(source_type as u8), None)
                        {
                            events.append(&mut output);
                        };
                    }
                };
            } else {
                if (!self.is_channel_active(modified_channel, socket_id)) {
                    return None;
                }
            }
        }

        modified_vel = clamp(modified_vel, self.min_velocity.unwrap_or(MIN_MIDI_VELOCITY), self.max_velocity.unwrap_or(MAX_MIDI_VELOCITY));

        // Is actually a note off
        if (modified_vel <= 0) {
            return self.note_off(channel, _key, socket_id, device_id, source_type);
        }

        // Don't trigger event to produce sound
        // if midi_output_only is enabled
        if (!self.midi_output_only) {
            self.send_event(
                MidiEvent::NoteOn {
                    channel: modified_channel,
                    key: modified_key,
                    vel: modified_vel,
                },
                socket_id,
            );
        }

        if self.is_client(socket_id) {
            let note_on_event = match self.get_program(modified_channel, socket_id) {
                None => PianoRhythmWebSocketMidiNoteOn {
                    channel: modified_channel,
                    note: modified_key,
                    velocity: modified_vel,
                    ..PianoRhythmWebSocketMidiNoteOn::default()
                },
                Some((current_bank, current_program)) => PianoRhythmWebSocketMidiNoteOn {
                    channel: modified_channel,
                    note: modified_key,
                    velocity: modified_vel,
                    program: Some(current_program),
                    volume: self.get_cc_value(modified_channel, 0x07, socket_id),
                    bank: Some(current_bank),
                    source: Some(source_type.to_u8()),
                    expression: self.get_cc_value(modified_channel, 0x0B, socket_id),
                    pan: self.get_cc_value(modified_channel, 0x0A, socket_id),
                },
            };

            if (!source_type.is_preview()) {
                self.broadcast_emit_event(&PianoRhythmWebSocketEmitEvent {
                    note_on: Some(note_on_event),
                    note_source: Some(source_type.to_u8()),
                    ..PianoRhythmWebSocketEmitEvent::default()
                });
            }

            // [Rendering] For mouse inputs, we want to display the actual key pressed,
            // and not the transposed/octaved key.
            if source_type.is_mouse() {
                modified_key = _key;
            }

            // Midi events are being handle elsewhere.
            if let Some(event) = self.create_synth_event(
                MidiMessage::NoteOn {
                    key: modified_key.into(),
                    vel: modified_vel.into(),
                },
                &[midi::NOTE_ON_BYTE + modified_channel, modified_key, modified_vel],
                modified_channel,
                socket_id,
                Some(source_type.to_u8()),
                device_id,
            ) {
                events.push(event)
            };
        }

        if events.len() > 0 {
            return Some(events);
        }

        return None;
    }

    pub fn ws_note_on(&mut self, event: PianoRhythmWebSocketMidiNoteOn, socket_id: &Option<u32>) {
        if !self.has_soundfont_loaded() || self.is_client(socket_id) || socket_id.is_none() {
            return;
        }

        let socket_id_ref = socket_id.unwrap();

        if (self.synth.is_socket_muted(&socket_id_ref)) {
            return;
        }

        let mut event_velocity = event.velocity;

        // Calculate velocity from percentage
        event_velocity = calculate_velocity_from_percentage(
            event_velocity as u32,
            Some(self.global_user_velocity),
            self.user_velocity_percentages.get(&socket_id_ref).cloned(),
        ) as u8;

        let sanitized_velocity = clamp(event_velocity, MIN_MIDI_VELOCITY, MAX_MIDI_VELOCITY);
        if (sanitized_velocity <= 0) {
            self.send_event(
                MidiEvent::NoteOff {
                    channel: event.channel,
                    key: event.note,
                },
                &socket_id,
            );
            return;
        }

        let current_program = self.get_program(event.channel, socket_id);
        let mut channel_has_preset_loaded = true;

        if let Some(ref id) = socket_id {
            channel_has_preset_loaded = self.synth.channel_has_preset_loaded(event.channel, id);
        }

        if (channel_has_preset_loaded && !self.is_channel_active(event.channel, socket_id)) {
            self.set_channel_active(event.channel, true, socket_id);
        }

        // Set Bank
        if (current_program.map(|cp| cp.0) != event.bank || !channel_has_preset_loaded) {
            if let Some(input_bank) = event.bank {
                let mut bank = input_bank;
                // Use default percussion bank, if possible, when bank is 0 on drum channel
                if (bank == 0 && event.channel == DRUM_CHANNEL as u8 && self.bank_exists(DEFAULT_PERCUSSION_KIT_BANK as u32)) {
                    bank = DEFAULT_PERCUSSION_KIT_BANK as u32;
                }

                if (self.bank_exists(bank)) {
                    self.bank_select(event.channel, bank.into(), &socket_id);
                }
            }
        }

        // Set Program
        if (current_program.map(|cp| cp.1) != event.program || !channel_has_preset_loaded) {
            if let Some(input_program) = event.program {
                let mut program = input_program as u32;

                if (self.preset_exists(program)) {
                    self.program_select(event.channel, program as u8, &socket_id);
                }
            }
        }

        // Set Volume
        if event.volume != self.get_cc_value(event.channel, piano_rhythm_midi_control_change::MAIN_VOLUME_MSB.into(), socket_id) {
            if let Some(volume) = event.volume {
                self.volume_change(event.channel, volume, socket_id);
            }
        }

        // Set Pan
        if event.pan != self.get_cc_value(event.channel, piano_rhythm_midi_control_change::PAN_MSB.into(), socket_id) {
            if let Some(pan) = event.pan {
                self.pan_change(event.channel, pan, socket_id);
            }
        }

        if (!self.midi_output_only) {
            self.send_event(
                MidiEvent::NoteOn {
                    channel: event.channel,
                    key: event.note,
                    vel: sanitized_velocity,
                },
                &socket_id,
            );
        }

        if let Some(event) = self.create_synth_event(
            MidiMessage::NoteOn {
                key: event.note.into(),
                vel: sanitized_velocity.into(),
            },
            &[midi::NOTE_ON_BYTE + event.channel, event.note, sanitized_velocity],
            event.channel,
            socket_id,
            event.source,
            None,
        ) {
            self.broadcast_synth_events(vec![event]);
        };
    }

    pub fn note_off(
        &mut self, channel: u8, key: u8, socket_id: &Option<u32>, device_id: Option<u32>, source_type: NoteSourceType,
    ) -> Option<Vec<PianoRhythmSynthEvent>> {
        let mut modified_key: u8 =
            // Do not modify key if it's not from the client or it's explicitly ignored
            if (source_type == NoteSourceType::Ignored || !self.is_client(socket_id)) {
                key
            } else {
                (key as i8).saturating_add(self.get_note_modifier()) as u8
            };

        let modified_channel = self.get_modified_channel(channel, modified_key, source_type, socket_id);

        if (!self.is_channel_active(modified_channel, socket_id)) {
            return None;
        }

        let mut events: Vec<PianoRhythmSynthEvent> = Vec::new();

        if (!self.midi_output_only) {
            self.send_event(
                MidiEvent::NoteOff {
                    channel: modified_channel,
                    key: modified_key,
                },
                socket_id,
            );
        }

        if self.is_client(socket_id) {
            if (!source_type.is_preview()) {
                self.broadcast_emit_event(&PianoRhythmWebSocketEmitEvent {
                    note_off: Some(PianoRhythmWebSocketMidiNoteOff {
                        channel: modified_channel,
                        note: modified_key,
                    }),
                    note_source: Some(source_type.to_u8()),
                    ..PianoRhythmWebSocketEmitEvent::default()
                });
            }
        }

        if source_type.is_mouse() {
            modified_key = key;
        }

        if let Some(event) = self.create_synth_event(
            MidiMessage::NoteOff {
                key: modified_key.into(),
                vel: 0.into(),
            },
            &[midi::NOTE_OFF_BYTE + modified_channel, modified_key, 0.into()],
            modified_channel,
            socket_id,
            Some(source_type as u8),
            device_id,
        ) {
            events.push(event);
        };

        if events.len() > 0 {
            return Some(events);
        }

        return None;
    }

    fn is_channel_active(&self, channel: u8, socket_id: &Option<u32>) -> bool {
        if let Some(ref id) = self.try_get_client_socket_id(socket_id) {
            return self.synth.is_channel_active(channel, id);
        }

        return false;
    }

    pub fn all_sounds_off(&mut self, channel: u8, socket_id: &Option<u32>) {
        if self.is_client(socket_id) {
            self.control_change(channel, MIDI_CONTROL_BYTES::ALL_SOUND_OFF, 0, socket_id, NoteSourceType::default());
        }

        self.damper_pedal(channel, 0, socket_id);
        self.send_event(MidiEvent::AllSoundOff { channel }, socket_id);
    }

    pub fn all_notes_off(&mut self, channel: u8, socket_id: &Option<u32>) {
        if self.is_client(socket_id) {
            self.control_change(channel, MIDI_CONTROL_BYTES::ALL_SOUND_OFF, 0, socket_id, NoteSourceType::default());
        }

        self.damper_pedal(channel, 0, socket_id);
        self.send_event(MidiEvent::AllNotesOff { channel }, socket_id);
    }

    pub fn reset_all_controllers(&mut self, channel: u8, socket_id: &Option<u32>) {
        if !self.has_soundfont_loaded() {
            return ();
        }

        self.control_change(channel, MIDI_CONTROL_BYTES::RESET_ALL_CONTROLLERS, 0, socket_id, NoteSourceType::default());
        self.damper_pedal(channel, 0, socket_id);
        self.send_event(MidiEvent::SystemResetWithChannel { channel }, socket_id);
    }

    pub fn volume_change(&mut self, channel: u8, value: u8, socket_id: &Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
        self.parse_midi_data(&[MIDI_CONTROL_CHANGE + channel, MIDI_CONTROL_BYTES::MAIN_VOLUME_MSB, value], socket_id, Some(3), None)
    }

    pub fn pan_change(&mut self, channel: u8, value: u8, socket_id: &Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
        // println!("PAN CHANGE: {:?} | {:?} | {:?}", &channel, &value, &socket_id);
        self.parse_midi_data(&[MIDI_CONTROL_CHANGE + channel, MIDI_CONTROL_BYTES::PAN_MSB, value], socket_id, Some(3), None)
    }

    pub fn damper_pedal(&mut self, channel: u8, value: u8, socket_id: &Option<u32>) {
        self.control_change(channel, MIDI_CONTROL_BYTES::DAMPER_PEDAL, value, socket_id, NoteSourceType::default());
    }

    pub fn clear_program_on_channel(&mut self, channel: u8, socket_id: &Option<u32>) {
        if let Some(ref id) = self.try_get_client_socket_id(socket_id) {
            self.synth.clear_program_on_channel(channel, id);

            if self.is_client(socket_id) {
                self.synth.get_audio_channel(channel, &id).inspect(|channel| {
                    self.broadcast_audio_channel_update(vec![channel.clone()]);
                });
            }
        }

        if self.is_client(socket_id) {
            if let Some(channel) = self.instrument_channels.get_mut(&channel) {
                channel.instrument = None;
            }
        }
    }

    fn is_instrument_loaded_in_channel(&self, channel: u8) -> bool {
        if let Some(channel) = self.instrument_channels.get(&channel) {
            return channel.instrument.is_some();
        }

        return false;
    }

    pub fn get_program(&self, channel: u8, socket_id: &Option<u32>) -> Option<(u32, u8)> {
        if self.has_soundfont_loaded() {
            if let Some(ref id) = self.try_get_client_socket_id(socket_id) {
                return self.synth.get_program(channel, id);
            }
        }

        return None;
    }

    fn get_cc_value(&self, channel: u8, ctrl: u16, socket_id: &Option<u32>) -> Option<u8> {
        if self.has_soundfont_loaded() {
            if let Some(ref id) = self.try_get_client_socket_id(socket_id) {
                return self.synth.get_cc_value(channel, ctrl, id);
            }
        }

        return None;
    }

    fn try_get_client_socket_id(&self, socket_id: &Option<u32>) -> Option<u32> {
        return socket_id.or(self.client_socket_id).clone();
    }

    pub fn control_change(&mut self, channel: u8, ctrl: u8, value: u8, socket_id: &Option<u32>, note_source: NoteSourceType) {
        self.send_event(MidiEvent::ControlChange { channel, ctrl, value }, &socket_id);

        if note_source.is_preview() {
            return;
        }

        if self.is_client(socket_id) {
            if !self.is_channel_active(channel, &socket_id) {
                return;
            }

            if ctrl == MIDI_CONTROL_BYTES::DAMPER_PEDAL {
                self.broadcast_emit_event(&PianoRhythmWebSocketEmitEvent {
                    sustain: Some(PianoRhythmWebSocketMidiSustain { channel, value }),
                    note_source: Some(note_source.to_u8()),
                    ..PianoRhythmWebSocketEmitEvent::default()
                })
            }

            if ctrl == MIDI_CONTROL_BYTES::ALL_NOTES_OFF || ctrl == MIDI_CONTROL_BYTES::OMNI_MODE_OFF {
                self.broadcast_emit_event(&PianoRhythmWebSocketEmitEvent {
                    all_notes_off: Some(channel),
                    note_source: Some(note_source.to_u8()),
                    ..PianoRhythmWebSocketEmitEvent::default()
                })
            }

            if ctrl == MIDI_CONTROL_BYTES::ALL_SOUND_OFF {
                self.broadcast_emit_event(&PianoRhythmWebSocketEmitEvent {
                    all_sound_off: Some(channel),
                    note_source: Some(note_source.to_u8()),
                    ..PianoRhythmWebSocketEmitEvent::default()
                })
            }
        }
    }

    pub fn channel_pressure(&mut self, channel: u8, value: u8, socket_id: &Option<u32>) {
        self.send_event(MidiEvent::ChannelPressure { channel, value }, socket_id);
    }

    pub fn send_pitch_bend_event(&mut self, channel: u8, value: u16, socket_id: &Option<u32>) {
        self.send_event(MidiEvent::PitchBend { channel, value }, socket_id);
    }

    fn pitch_bend(&mut self, channel: u8, value: u16, socket_id: &Option<u32>) -> Option<PianoRhythmSynthEvent> {
        self.send_pitch_bend_event(channel, value, socket_id);

        if self.is_client(socket_id) {
            if let Some(_id) = socket_id {
                self.broadcast_emit_event(&PianoRhythmWebSocketEmitEvent {
                    pitch_bend: Some(crate::types::PianoRhythmWebSocketMidiPitchBend {
                        channel,
                        value: value as u32,
                    }),
                    note_source: Some(NoteSourceType::Midi.to_u8()),
                    ..PianoRhythmWebSocketEmitEvent::default()
                });

                return Some(PianoRhythmSynthEvent {
                    channel,
                    socket_id: socket_id.clone(),
                    current_pitch: Some(value as u32),
                    source: Some(NoteSourceType::Midi.to_u8()),
                    is_client: true,
                    ..Default::default()
                });
            }
        }

        None
    }

    pub fn poly_key_pressure(&mut self, channel: u8, key: u8, value: u8, socket_id: &Option<u32>) {
        self.send_event(MidiEvent::PolyphonicKeyPressure { channel, key, value }, socket_id);
    }

    pub fn bank_select(&mut self, channel: u8, bank: u32, socket_id: &Option<u32>) {
        if !self.has_soundfont_loaded() {
            return;
        }

        if let Some(ref id) = self.try_get_client_socket_id(socket_id) {
            if !self.has_socket_id(id) {
                return ();
            }

            if let Err(err) = self.synth.bank_select_with_channel(channel, bank, id) {
                log::error!("Failed to select bank. Channel: {} | Bank: {} | SocketID: {:?} -> Error: {err:?}", channel, bank, socket_id)
            }
        }
    }

    pub fn set_channel_active(&mut self, channel: u8, value: bool, socket_id: &Option<u32>) {
        if let Some(ref id) = socket_id.or(self.client_socket_id) {
            self.synth.set_channel_active(channel, value, id);
            self.synth.get_audio_channel(channel, &id).inspect(|channel| {
                self.broadcast_audio_channel_update(vec![channel.clone()]);
            });
        }
    }

    pub fn synth_set_sample_rate(&mut self, sample_rate: f32) {
        self.synth.set_sample_rate(sample_rate);
        self.sample_rate = sample_rate;
    }

    pub fn synth_get_sample_rate(&self) -> f32 {
        self.sample_rate.clone()
    }

    pub fn synth_set_polyphony(&mut self, value: u16) {
        self.synth.synth_set_polyphony(value);
    }

    pub fn synth_set_gain(&mut self, value: f32) {
        self.synth.synth_set_gain(value)
    }

    pub fn synth_set_reverb(&mut self, value: bool) {
        self.synth.set_reverb_active(value)
    }

    pub fn synth_set_chorus(&mut self, value: bool) {
        self.synth.set_chorus_active(value)
    }

    pub fn synth_get_reverb_level(&self) -> f32 {
        self.synth.get_reverb_level()
    }

    pub fn synth_set_reverb_level(&mut self, value: f32) {
        self.synth.set_reverb_level(value)
    }

    pub fn synth_get_reverb_room_size(&self) -> f32 {
        self.synth.get_reverb_room_size()
    }

    pub fn synth_set_reverb_room_size(&mut self, value: f32) {
        self.synth.set_reverb_room_size(value)
    }

    pub fn synth_get_reverb_damp(&self) -> f32 {
        self.synth.get_reverb_damp()
    }

    pub fn synth_set_reverb_damp(&mut self, value: f32) {
        self.synth.set_reverb_damp(value)
    }

    pub fn synth_get_reverb_width(&self) -> f32 {
        self.synth.get_reverb_width()
    }

    pub fn synth_set_reverb_width(&mut self, value: f32) {
        self.synth.set_reverb_width(value)
    }

    pub fn set_drum_channel_muted(&mut self, value: bool) {
        self.drum_channel_muted = value
    }

    pub fn set_equalizer_enabled(&mut self, value: bool) {
        self.equalizer_enabled = value;
    }

    pub fn set_midi_output_only(&mut self, value: bool) {
        self.midi_output_only = value
    }

    pub fn set_octave_offset(&mut self, value: i8) {
        if let Ok(val) = OCTAVE_OFFSET_RANGE.check(value) {
            self.octave_offset = val;
        }
    }

    pub fn set_transpose_offset(&mut self, value: i8) {
        if let Ok(val) = TRANSPOSE_OFFSET_RANGE.check(value) {
            self.transpose_offset = val;
        }
    }

    pub fn set_max_multi_mode_channels(&mut self, value: u8) {
        if let Ok(val) = MULTI_MODE_MAX_CHANNEL_RANGE.check(value) {
            self.max_multi_mode_channels = val;
        }
    }

    pub fn set_disable_velocity_for_client(&mut self, value: bool) {
        self.disable_velocity_for_client = value;
    }

    pub fn set_slot_mode(&mut self, target: ActiveChannelsMode) {
        if (self.slot_mode == target) {
            return;
        }

        self.slot_mode = target;

        match self.slot_mode {
            ActiveChannelsMode::SPLIT2 | ActiveChannelsMode::SPLIT4 | ActiveChannelsMode::SPLIT8 => {
                let vals: Vec<u8> = (pianorhythm_shared::midi::MIN_NOTE..=pianorhythm_shared::midi::MAX_NOTE).collect();
                let split_size = match self.slot_mode {
                    ActiveChannelsMode::SPLIT2 => pianorhythm_shared::midi::SLOT_MODE_SPLIT2_MAX_CHANNEL,
                    ActiveChannelsMode::SPLIT4 => pianorhythm_shared::midi::SLOT_MODE_SPLIT4_MAX_CHANNEL,
                    ActiveChannelsMode::SPLIT8 => pianorhythm_shared::midi::SLOT_MODE_SPLIT8_MAX_CHANNEL,
                    _ => pianorhythm_shared::midi::SLOT_MODE_SINGLE_MAX_CHANNEL,
                };
                let target_chunk_size = vals.len() as u8 / split_size;
                let chunks: Vec<Vec<u8>> = vals.chunks(target_chunk_size.into()).map(|s| s.into()).collect();
                self.split_keys = chunks;
            }
            _ => {
                self.split_keys = Default::default();
            }
        }
    }

    pub fn set_primary_channel(&mut self, value: u8) {
        self.primary_channel = value;
    }

    pub fn disconnect(&mut self) {
        self.reset();
    }

    pub fn reset(&mut self) {
        self.synth_set_gain(1.0);
        self.synth.clear_sockets();
        self.synth.reset_voices();
        self.broadcast_all_client_channels_update();
    }

    pub fn dispose(&mut self) {
        self.disconnect();
        self.synth.dispose();
    }

    pub fn set_interpolation_method(&mut self, interpolation_method: u32) {
        if let Some(ref id) = self.get_client_socket_id() {
            let method: InterpolationMethod = match interpolation_method {
                0 => InterpolationMethod::None,
                1 => InterpolationMethod::Linear,
                2 => InterpolationMethod::FourthOrder,
                3 => InterpolationMethod::SeventhOrder,
                _ => InterpolationMethod::default(),
            };

            // Update client's
            self.synth.set_user_interp_method(None, &id.clone(), method);

            // Update other users
            self.synth.update_synth_players_interpolation_method(method);
        }
    }

    pub fn get_client_socket_id(&self) -> &Option<u32> {
        &self.client_socket_id
    }

    fn get_note_modifier(&self) -> i8 {
        (12 * self.octave_offset) + self.transpose_offset
    }

    pub fn set_auto_fill_channels_with_default(&mut self, value: bool) {
        self.midi_auto_fill_empty_channels_with_default_instrument = value;
    }

    pub fn is_socket_muted(&self, socket_id: &Option<u32>) -> bool {
        if let Some(ref id) = socket_id.or(self.client_socket_id) {
            return (self.synth.is_socket_muted(id));
        }

        false
    }

    pub fn set_max_velocity(&mut self, value: Option<u8>) {
        self.max_velocity = value;
    }

    pub fn set_min_velocity(&mut self, value: Option<u8>) {
        self.min_velocity = value;
    }

    pub fn set_use_default_instrument_when_missing_for_other_users(&mut self, value: bool) {
        self.use_default_instrument_when_missing_for_other_users = value;
    }

    pub fn set_max_note_on_time(&mut self, value: Option<f64>) {
        self.synth.set_max_note_on_time(value);
    }

    fn bank_exists(&self, bank: u32) -> bool {
        self.loaded_banks.iter().any(|pre| pre == &bank)
    }

    fn preset_exists(&self, preset: u32) -> bool {
        self.loaded_presets.iter().any(|pre| pre == &preset)
    }

    pub fn get_all_audio_channels(&self, socket_id: &Option<u32>) -> Vec<AudioChannel> {
        let mut channels: Vec<AudioChannel> = vec![];

        if let Some(ref _id) = socket_id.or(self.client_socket_id) {
            for i in 0..=MAX_MIDI_CHANNEL {
                self.synth.get_audio_channel(i as u8, _id).inspect(|channel| {
                    channels.push(channel.clone());
                });
            }
        }

        channels
    }

    pub fn get_audio_channel(&self, channel: u8, socket_id: &Option<u32>) -> Option<AudioChannel> {
        if let Some(ref _id) = socket_id.or(self.client_socket_id) {
            self.synth.get_audio_channel(channel, _id)
        } else {
            None
        }
    }
}

// Client related methods
impl PianoRhythmSynthesizer {
    pub fn client_clear_all_audio_channels(&mut self) {
        let client_socket_id = self.client_socket_id.clone();
        for i in 0..=MAX_MIDI_CHANNEL {
            let channel = i as u8;
            self.reset_all_controllers(channel, &client_socket_id);
            self.clear_program_on_channel(channel, &client_socket_id);
        }
    }

    pub fn client_reset_channels_to_default(&mut self) {
        self.client_clear_all_audio_channels();
        let client_socket_id = self.client_socket_id.clone();

        let default_primary_channel = pianorhythm_shared::audio::DEFAULT_PRIMARY_CHANNEL;
        self.set_primary_channel(default_primary_channel);
        self.bank_select(default_primary_channel, self.loaded_banks.first().map(|x| *x).unwrap_or(0u32), &client_socket_id);
        self.program_select(default_primary_channel, self.loaded_presets.first().map(|x| *x as u8).unwrap_or(0u8) as u8, &client_socket_id);
        self.set_channel_active(default_primary_channel, true, &client_socket_id);
        self.broadcast_all_client_channels_update();
    }

    pub fn client_set_instrument_on_channel(&mut self, payload: &SetChannelInstrumentPayload) {
        if self.loaded_banks.is_empty() || self.loaded_presets.is_empty() {
            return;
        }

        let client_socket_id = self.client_socket_id.clone();
        let mut target_bank = payload.get_bank();

        // Check for bank. Use default if applicable
        if !self.loaded_banks.contains(&target_bank) {
            if self.use_default_bank_when_missing {
                target_bank = self.loaded_banks.first().cloned().unwrap_or_default();
            } else {
                return;
            }
        }

        // Check for preset
        if !self.loaded_presets.contains(&payload.get_preset()) {
            return;
        }

        let audio_channels = self.get_all_audio_channels(&client_socket_id);
        let current_slot_mode = &self.slot_mode;

        if let Some(audio_channel) = audio_channels.iter().find(|x| match payload.get_field_type() {
            SetChannelInstrumentType::Add => x.channel == payload.get_channel(),
            SetChannelInstrumentType::NextInactive => !x.active,
            SetChannelInstrumentType::NextEmpty => !x.has_instrument(),
        }) {
            let channel = audio_channel.get_channel() as u8;
            let mut can_update = true;

            let disabled_channels = get_disabled_channels(&audio_channels, Some(current_slot_mode.clone()), self.max_multi_mode_channels);

            // Check if channel is disabled
            if can_update && disabled_channels.contains(&(channel as u32)) {
                can_update = false;
            }

            // Check if channel already has instrument
            if can_update
                && audio_channel.has_instrument()
                && audio_channel.get_bank() == payload.get_bank()
                && audio_channel.get_preset() == payload.get_preset()
            {
                can_update = false;
            }

            if can_update {
                self.bank_select(channel, target_bank, &client_socket_id);
                self.program_select(channel, payload.get_preset() as u8, &client_socket_id);
                self.set_channel_active(channel, true, &client_socket_id);

                if payload.has_pan() {
                    self.pan_change(channel, payload.get_pan() as u8, &client_socket_id);
                }

                if payload.has_volume() {
                    self.volume_change(channel, payload.get_volume() as u8, &client_socket_id);
                }

                self.broadcast_client_audio_channel_update(channel);
            }
        }
    }
}

fn get_active_channels_from_split(slot_mode: Option<ActiveChannelsMode>) -> u32 {
    match slot_mode {
        Some(ActiveChannelsMode::SPLIT2) => pianorhythm_shared::midi::SLOT_MODE_SPLIT2_MAX_CHANNEL as u32,
        Some(ActiveChannelsMode::SPLIT4) => pianorhythm_shared::midi::SLOT_MODE_SPLIT4_MAX_CHANNEL as u32,
        Some(ActiveChannelsMode::SPLIT8) => pianorhythm_shared::midi::SLOT_MODE_SPLIT8_MAX_CHANNEL as u32,
        Some(ActiveChannelsMode::SINGLE) => pianorhythm_shared::midi::SLOT_MODE_SINGLE_MAX_CHANNEL as u32,
        _ => 0,
    }
}

fn is_channel_disabled(channel: u8, slot_mode: Option<ActiveChannelsMode>) -> bool {
    let split_size = get_active_channels_from_split(slot_mode);
    split_size != 0 && channel >= split_size as u8
}

fn get_disabled_channels(channels: &Vec<AudioChannel>, slot_mode: Option<ActiveChannelsMode>, multi_mode_max_channel: u8) -> Vec<u32> {
    match slot_mode {
        Some(ActiveChannelsMode::MULTI) => {
            let max_channel = num_traits::clamp(multi_mode_max_channel, 1, pianorhythm_shared::midi::MAX_CHANNEL) - 1;
            get_channel_numbers(&channels).into_iter().filter(|&x| x > max_channel as u32).collect()
        }
        _ => {
            let split_size = get_active_channels_from_split(slot_mode);
            get_channel_numbers(&channels)
                .into_iter()
                .filter(|&x| x >= split_size && split_size != 0)
                .collect()
        }
    }
}

fn get_channel_numbers(channels: &Vec<AudioChannel>) -> Vec<u32> {
    channels.iter().map(|x| x.channel).collect()
}

pub fn get_velocity_percentage(velocity: u32, percentage: u32) -> u32 {
    ((percentage as f32 / 100.0f32) * velocity as f32).round() as u32
}

pub fn calculate_velocity_from_percentage(velocity: u32, global_velocity_percentage: Option<u32>, user_velocity_percentage: Option<u32>) -> u32 {
    match global_velocity_percentage {
        None => velocity,
        Some(global_velocity_percentage) => match user_velocity_percentage {
            Some(user_velocity_percentage) if user_velocity_percentage != pianorhythm_shared::audio::MAX_VELOCITY_USER_PERCENTAGE as u32 => {
                get_velocity_percentage(velocity, user_velocity_percentage)
            }
            _ => get_velocity_percentage(velocity, global_velocity_percentage),
        },
    }
}

fn midi_message_to_event(channel: u8, message: MidiMessage) -> Option<MidiEvent> {
    match message {
        MidiMessage::NoteOn { key, vel } => {
            if vel == 0 {
                Some(MidiEvent::NoteOff { channel, key: key.as_int() })
            } else {
                Some(MidiEvent::NoteOn {
                    channel,
                    vel: vel.as_int(),
                    key: key.as_int(),
                })
            }
        }
        MidiMessage::NoteOff { key, vel } => Some(MidiEvent::NoteOff { channel, key: key.as_int() }),
        MidiMessage::ProgramChange { program } => Some(MidiEvent::ProgramChange {
            channel,
            program_id: program.as_int(),
        }),
        MidiMessage::Controller { controller, value } if controller.as_int() == piano_rhythm_midi_control_change::ALL_SOUND_OFF => {
            Some(MidiEvent::AllSoundOff { channel })
        }
        MidiMessage::Controller { controller, value } if controller.as_int() == piano_rhythm_midi_control_change::ALL_NOTES_OFF => {
            Some(MidiEvent::AllNotesOff { channel })
        }
        MidiMessage::Controller { controller, value } if controller.as_int() == piano_rhythm_midi_control_change::RESET_ALL_CONTROLLERS => {
            Some(MidiEvent::SystemReset)
        }
        MidiMessage::Controller { controller, value } => Some(MidiEvent::ControlChange {
            channel,
            ctrl: controller.as_int(),
            value: value.as_int(),
        }),
        MidiMessage::PitchBend { bend } => Some(MidiEvent::PitchBend {
            channel,
            value: bend.as_int() as u16,
        }),
        MidiMessage::Aftertouch { key, vel } => Some(MidiEvent::PolyphonicKeyPressure {
            channel,
            key: key.as_int(),
            value: vel.as_int(),
        }),
        _ => None,
    }
}

#[cfg(target_arch = "wasm32")]
pub async fn create_audio_worklet(
    ctx: AudioContext, 
    process: Box<dyn FnMut(&mut [f32], &mut [f32]) -> ()>,
    parse_midi_data: Box<dyn Fn(&[u8], Option<u32>, Option<u8>, Option<u32>) -> ()>,
    handle_midi_input_connection: Box<dyn Fn(String, bool) -> ()>,
    handle_midi_output_connection: Box<dyn Fn(String, bool) -> ()>, number_of_channels: Option<u32>, buffer_size: Option<i32>,
) -> Result<AudioWorkletNode, JsValue> {
    let node = wasm_audio_node(&ctx, process, parse_midi_data, handle_midi_input_connection, handle_midi_output_connection, number_of_channels, buffer_size)?;
    node.connect_with_audio_node(&ctx.destination())?;

    return Ok(node);
}

#[cfg(target_arch = "wasm32")]
pub async fn initialize(ctx: AudioContext) -> Result<(), JsValue> {
    Ok(())
}

fn midi_parse(event: &[u8]) -> Option<(u8, MidiMessage)> {
    if let Ok(event) = LiveEvent::parse(event) {
        return match event {
            LiveEvent::Midi { channel, message } => Some((channel.as_int(), message)),
            LiveEvent::Common(value) => {
                // log::debug!("[midi_parse] Common: {:?}", &value);
                None
            }
            LiveEvent::Realtime(value) => {
                // log::debug!("[midi_parse] REAL TIME: {:?}", &value);
                None
            }
            _ => None,
        };
    }

    None
}

fn apply_velocity_curve(input_velocity: u8) -> u8 {
    // Example of a quadratic curve for softer response
    let normalized = input_velocity as f32 / 127.0;
    let curved = normalized.powf(2.0); // Exponentiate to emphasize lower velocities
    ((curved * 127.0) as u8).clamp(MIN_MIDI_VELOCITY, MAX_MIDI_VELOCITY)
}
