#![feature(try_find)]
#![allow(dead_code, unused)]

#[macro_use]
extern crate lazy_static;
#[cfg(target_arch = "wasm32")]
#[macro_use]
extern crate wasm_bindgen;

use std::cell::RefCell;
use std::collections::HashMap;
use std::convert::TryInto;
use std::hash::{Hash, Hasher};
use std::io::Cursor;
use std::rc::Rc;
use std::sync::{Arc, OnceLock};

use cached::proc_macro::cached;
#[cfg(target_arch = "wasm32")]
use cpal::traits::{DeviceTrait, HostTrait, StreamTrait};
#[cfg(target_arch = "wasm32")]
use cpal::{FromSample, Sample, SizedSample, Stream, StreamConfig};
#[cfg(target_arch = "wasm32")]
use gloo::utils::window;
#[cfg(target_arch = "wasm32")]
use js_sys::{Array, Reflect};
use midir::{ConnectErrorKind, Ignore, InitError, MidiInput, MidiInputConnection, MidiOutput};
use once_cell::sync::{Lazy, OnceCell};
use protobuf::{Message, ProtobufEnum};
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::{prelude::*, JsValue};
#[cfg(target_arch = "wasm32")]
use web_sys::{console, AudioContext, AudioWorkletNode};

use pianorhythm_proto::midi_renditions::{ActiveChannelsMode, AudioChannel, SetChannelInstrumentPayload};
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use simple_eq::design::Curve;

// use crate::isynthesizers::rustysynth::RustySynth;

pub use self::pianorhythm_synth::*;
pub use self::recorder::*;
pub use self::sequencer::*;
pub use self::sequencer_vp::*;
pub use self::synth::*;
pub use self::types::*;

mod core;
mod dependent_module;
mod isynthesizers;
mod pianorhythm_synth;
mod recorder;
mod sequencer;
mod sequencer_vp;
mod state;
mod synth;
mod types;
mod utils;
#[cfg(target_arch = "wasm32")]
mod wasm_audio;

pub const BUFFER_SIZE: usize = 64;

pub static mut MIDI_STATE: Lazy<MidiState> = Lazy::new(|| MidiState::default());
pub static mut SYNTH: OnceLock<PianoRhythmSynthesizer> = OnceLock::new();

#[cached]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn hash_device_id(s: String) -> u32 {
    let mut hash: u32 = 5381;
    for c in s.chars() {
        hash = (hash << 5).wrapping_add(hash).wrapping_add(c as u32);
    }
    hash
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_main_js() -> Result<(), JsValue> {
    // This provides better error messages in debug mode.
    // It's disabled in release mode so it doesn't bloat up the file size.
    #[cfg(debug_assertions)]
    console_error_panic_hook::set_once();

    log::trace!("Synth main called.");
    Ok(())
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn create_synth_desktop(
    options: PianoRhythmSynthesizerDescriptor, emit_to_websocket_cb: Option<WebsocketEmitEventCallback>,
    synth_events_cb: Option<PianoRhythmSynthEventCallback>, audio_channel_update_cb: Option<PianoRhythmAudioChannelUpdateCallback>,
) -> Result<(), String> {
    unsafe {
        SYNTH.set(PianoRhythmSynthesizer::new(options, emit_to_websocket_cb, synth_events_cb, audio_channel_update_cb));
    }

    Ok(())
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn process(output: &mut [f32]) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.process(output);
        }
    }
}

pub fn process_stereo(output_l: &mut [f32], output_r: &mut [f32]) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.process_stereo(output_l, output_r);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_program(_channel: u8, socket_id: Option<u32>) -> Option<PianoRhythmCurrentProgram> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.get_program(_channel, &socket_id).map(|(bank, program)| PianoRhythmCurrentProgram {
                channel: _channel,
                bank,
                program,
            });
        }
    }

    None
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn parse_midi_data(data: &[u8], socket_id: Option<u32>, source: Option<u8>, device_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.parse_midi_data(data, &socket_id, source, device_id);
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn parse_midi_data(data: &[u8], socket_id: Option<u32>, source: Option<u8>, device_id: Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.parse_midi_data(data, &socket_id, source, device_id);
        }

        None
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn note_on(channel: u8, key: u8, vel: u8, socket_id: Option<u32>, source: u8) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.parse_midi_data(&[pianorhythm_shared::midi::NOTE_ON_BYTE + channel, key, vel], &socket_id, Some(source), None);
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn note_on_desktop(channel: u8, key: u8, vel: u8, socket_id: Option<u32>, source: u8) -> Option<Vec<PianoRhythmSynthEvent>> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.parse_midi_data_no_broadcast(&[pianorhythm_shared::midi::NOTE_ON_BYTE + channel, key, vel], &socket_id, Some(source), None)
        } else {
            None
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_ws_socket_note_on(event: PianoRhythmWebSocketMidiNoteOn, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.ws_note_on(event, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_ws_socket_pitch(event: PianoRhythmWebSocketMidiPitchBend, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            if socket_id.is_none() {
                return;
            }

            x.send_pitch_bend_event(event.channel as u8, event.value as u16, &socket_id);
        }
    }
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn from_socket_pitch(event: &JsValue, socket_id: Option<u32>) {
    if let Ok(output) = serde_wasm_bindgen::from_value(event.into()) {
        synth_ws_socket_pitch(output, socket_id);
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn from_socket_pitch(event: PianoRhythmWebSocketMidiPitchBend, socket_id: Option<u32>) {
    synth_ws_socket_pitch(event, socket_id);
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn from_socket_note_on(event: &JsValue, socket_id: Option<u32>) {
    if let Ok(output) = serde_wasm_bindgen::from_value(event.into()) {
        synth_ws_socket_note_on(output, socket_id);
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn from_socket_note_on(event: PianoRhythmWebSocketMidiNoteOn, socket_id: Option<u32>) {
    synth_ws_socket_note_on(event, socket_id);
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn note_off(channel: u8, key: u8, socket_id: Option<u32>, source: u8) {
    unsafe {
        if let Some(mut x) = SYNTH.get_mut() {
            x.parse_midi_data(&[pianorhythm_shared::midi::NOTE_OFF_BYTE + channel, key, 0], &socket_id, Some(source), None);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_user_velocity_percentage(value: u32, socket_id: Option<u32>) {
    unsafe {
        if let Some(mut x) = SYNTH.get_mut() {
            if let Some(ref id) = socket_id {
                x.set_socket_velocity_percentage(&id, value);
            }
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_user_volume(value: u32, socket_id: Option<u32>) {
    unsafe {
        if let Some(mut x) = SYNTH.get_mut() {
            if let Some(ref id) = socket_id {
                x.set_socket_volume(&id, value);
            }
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn from_socket_note_off(channel: u8, key: u8, socket_id: Option<u32>) {
    unsafe {
        if let Some(mut x) = SYNTH.get_mut() {
            x.note_off(channel, key, &socket_id, None, NoteSourceType::Midi);
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn note_off_desktop(channel: u8, key: u8, socket_id: Option<u32>, source: u8) -> Option<Vec<PianoRhythmSynthEvent>> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.parse_midi_data_no_broadcast(&[pianorhythm_shared::midi::NOTE_OFF_BYTE + channel, key, 0], &socket_id, Some(source), None)
        } else {
            None
        }
    }
}

// ---- Volume Change ----- //
fn _volume_change(channel: u8, value: u8, socket_id: Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
    unsafe {
        if let Some(mut x) = SYNTH.get_mut() {
            return x.volume_change(channel, value, &socket_id);
        }
    }

    None
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn volume_change(channel: u8, value: u8, socket_id: Option<u32>) {
    _volume_change(channel, value, socket_id);
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn volume_change(channel: u8, value: u8, socket_id: Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
    _volume_change(channel, value, socket_id)
}
// ---- --------- ----- //

// ---- Pan Change ----- //
fn _pan_change(channel: u8, value: u8, socket_id: Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.pan_change(channel, value, &socket_id);
        }
    }

    None
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn pan_change(channel: u8, value: u8, socket_id: Option<u32>) {
    _pan_change(channel, value, socket_id);
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn pan_change(channel: u8, value: u8, socket_id: Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
    _pan_change(channel, value, socket_id)
}
// ---- --------- ----- //

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_gain(value: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_gain(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_user_gain(value: f32, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_user_gain(value, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn all_notes_off(channel: u8, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.all_notes_off(channel, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn all_sounds_off(channel: u8, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.all_sounds_off(channel, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn mute_user(socket_id: Option<u32>, value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.mute_socket(&socket_id, value);
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
type HandleMidiOutputResult = Result<MidiInputConnection<()>, String>;

#[cfg(target_arch = "wasm32")]
type HandleMidiOutputResult = Result<(), String>;

pub fn on_handle_midi_input(midi_id: &str, midi_in: Result<MidiInput, InitError>) -> HandleMidiOutputResult {
    #[cfg(not(target_arch = "wasm32"))]
    let mut output: Result<MidiInputConnection<()>, String> = Err("Unknown error.".to_string());

    #[cfg(target_arch = "wasm32")]
    let mut output: Result<(), String> = Err("Unknown error.".to_string());

    match midi_in {
        Ok(mut midi_in) => {
            midi_in.ignore(Ignore::All);
            let midi_in_ports = midi_in.ports();

            let port = midi_in_ports.into_iter().find(|x| {
                if let Ok(port_name) = midi_in.port_name(x) {
                    return port_name == *midi_id;
                }
                return false;
            });

            match port {
                Some(port) => {
                    let port_name = midi_in.port_name(&port).unwrap_or("Invalid port name".to_string());

                    #[cfg(target_arch = "wasm32")]
                    log::debug!("Attempting to connect to port {:?}", port_name);

                    let device_id = hash_device_id(midi_id.to_string());

                    let midi_in_conn = midi_in.connect(
                        &port,
                        "midir",
                        move |us, message, _| unsafe {
                            if let Some(x) = SYNTH.get_mut() {
                                // #[cfg(target_arch = "wasm32")] {
                                //     log::debug!("Input {:?} | {} | {} | {}",
                                //         message,
                                //         is_main_thread_scope(),
                                //         is_worker_global_scope(),
                                //         is_audio_worklet_scope()
                                //     );
                                // }
                                _ = x.parse_midi_data(message, &None, Some(NoteSourceType::Midi.to_u8()), Some(device_id));
                            }
                        },
                        (),
                    );

                    match midi_in_conn {
                        Ok(midi_in_conn) => {
                            #[cfg(not(target_arch = "wasm32"))]
                            {
                                output = Ok(midi_in_conn);
                            }

                            #[cfg(target_arch = "wasm32")]
                            unsafe {
                                if let Some(x) = SYNTH.get_mut() {
                                    log::debug!("Connected to port: {:?}", port_name);
                                    MIDI_STATE.inputs.lock().unwrap().insert(midi_id.to_string(), midi_in_conn);
                                    output = Ok(());
                                } else {
                                    log::error!("Failed to connect to port: {:?}", port_name);
                                    output = Err("Synthesizer not initialized.".to_string());
                                }
                            }
                        }
                        Err(e) => {
                            #[cfg(target_arch = "wasm32")]
                            log::error!("Midi Input Connection Error: {:?} | {:?}", &e, &midi_id);
                            let connect_error_message = match e.kind() {
                                ConnectErrorKind::Other(msg) => msg,
                                _ => "Invalid port",
                            };
                            output = Err(connect_error_message.to_string());
                        }
                    }
                }
                None => {
                    #[cfg(target_arch = "wasm32")]
                    log::warn!("No port found at index {}", midi_id);
                    output = Err("No port found at index.".to_string());
                }
            }
        }
        Err(e) => {
            #[cfg(target_arch = "wasm32")]
            log::error!("Midi Input Error: {} | {}", e, midi_id);
            output = Err("Failed to initialize.".to_string());
        }
    }

    output
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn open_midi_input_connection(midi_id: String) -> Result<(), String> {
    unsafe {
        if MIDI_STATE.inputs.lock().unwrap().contains_key(&midi_id) {
            return Err("Already connected.".to_string());
        }

        let mut midi_in = MidiInput::new("pianorhythm-input");
        return on_handle_midi_input(&midi_id, midi_in).map(|_| ());
    }
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn open_midi_output_connection(midi_id: String) -> Option<String> {
    let mut output = None;
    unsafe {
        if MIDI_STATE.outputs.lock().unwrap().contains_key(&midi_id) {
            return output;
        }

        let midi_out = MidiOutput::new("pianorhythm-output");
        match midi_out {
            Ok(midi_out) => {
                let midi_out_ports = midi_out.ports();
                let port = midi_out_ports.into_iter().find(|x| {
                    if let Ok(port_name) = midi_out.port_name(x) {
                        return port_name == midi_id;
                    }
                    return false;
                });

                match port {
                    Some(port) => {
                        let midi_out_conn = midi_out.connect(&port, "pianorhythm-midir-output");
                        match midi_out_conn {
                            Ok(mut midi_out_conn) => {
                                if let Some(x) = SYNTH.get_mut() {
                                    MIDI_STATE.outputs.lock().unwrap().insert(midi_id, midi_out_conn);
                                } else {
                                    output = Some("Synthesizer failed.".to_string());
                                }
                            }
                            Err(e) => {
                                #[cfg(target_arch = "wasm32")]
                                log::error!("Midi Output Connection Error: {} | {}", e, midi_id);
                                let connect_error_message = match e.kind() {
                                    ConnectErrorKind::Other(msg) => msg,
                                    _ => "Invalid port",
                                };
                                output = Some(connect_error_message.to_string());
                            }
                        }
                    }
                    None => {
                        log::info!("No port found at index {}", midi_id);
                        output = Some("No port found at index.".to_string());
                    }
                }
            }
            Err(e) => {
                #[cfg(target_arch = "wasm32")]
                log::error!("Midi Output Error: {} | {}", e, midi_id);
                output = Some("Failed to initialize.".to_string());
            }
        }
    }

    return output;
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn close_midi_input_connection(midi_id: String) {
    unsafe {
        if let Some(input) = MIDI_STATE.inputs.lock().unwrap().remove(&midi_id) {
            input.close();
        };
    }
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn close_midi_output_connection(midi_id: String) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            if let Some(output) = MIDI_STATE.outputs.lock().unwrap().remove(&midi_id) {
                output.close();
            }
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn list_midi_input_connections() -> HashMap<usize, String> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.list_midi_input_connections()
        } else {
            HashMap::new()
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn list_midi_output_connections() -> HashMap<usize, String> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.list_midi_output_connections()
        } else {
            HashMap::new()
        }
    }
}

#[inline]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_get_reverb_level() -> f32 {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_get_reverb_level()
        } else {
            0.0
        }
    }
}

#[inline]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_get_reverb_room_size() -> f32 {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_get_reverb_room_size()
        } else {
            0.0
        }
    }
}

#[inline]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_get_reverb_damp() -> f32 {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_get_reverb_damp()
        } else {
            0.0
        }
    }
}

#[inline]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_get_reverb_width() -> f32 {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_get_reverb_width()
        } else {
            0.0
        }
    }
}

#[inline]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_reverb_level(value: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_reverb_level(value);
        }
    };
}

#[inline]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_reverb_room_size(value: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_reverb_room_size(value);
        }
    };
}

#[inline]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_reverb_damp(value: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_reverb_damp(value);
        }
    };
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_reverb_width(value: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_reverb_width(value);
        }
    };
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn list_midi_input_connections() -> JsValue {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.list_midi_input_connections();
        } else {
            return JsValue::NULL;
        }
    };
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn list_midi_output_connections() -> JsValue {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.list_midi_output_connections();
        } else {
            return JsValue::NULL;
        }
    };
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn emit_to_midi_output(midi_id: String, data: &[u8]) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            if let Some(output) = MIDI_STATE.outputs.lock().unwrap().get_mut(&midi_id) {
                output.send(&[pianorhythm_shared::midi::NOTE_ON_BYTE, 53, 100]).unwrap_or_else(|err| {
                    #[cfg(target_arch = "wasm32")]
                    log::error!("Error when forwarding message... | Data: {:?} | Error: {}", data, err)
                });
            }
        }
    };
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn reset_all_controllers(channel: u8, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.reset_all_controllers(channel, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn client_reset_all_controllers() {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            for channel in 0..=pianorhythm_shared::midi::MAX_CHANNEL {
                x.reset_all_controllers(channel, &None);
            }
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn program_select(channel: u8, preset_id: u8, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.parse_midi_data(&[192 + channel, preset_id, 0], &socket_id, None, None);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn bank_select(channel: u8, bank_id: u32, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.bank_select(channel, bank_id, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_channel_active(channel: u8, value: bool, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_channel_active(channel, value, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn damper_pedal(channel: u8, value: u8, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.damper_pedal(channel, value, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_polyphony(value: u16) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_polyphony(value);
        }
    }
}

pub fn get_all_audio_channels(socket_id: Option<u32>) -> Vec<AudioChannel> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.get_all_audio_channels(&socket_id);
        }
    }

    vec![]
}

pub fn get_audio_channel(channel: u8, socket_id: Option<u32>) -> Option<AudioChannel> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.get_audio_channel(channel, &socket_id);
        }
    }

    None
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn clear_program_on_channel(channel: u8, socket_id: Option<u32>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.clear_program_on_channel(channel, &socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_reverb(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_reverb(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_auto_fill_channels_with_default(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_auto_fill_channels_with_default(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_set_chorus(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.synth_set_chorus(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn load_soundfont(buf: &[u8]) -> Result<(), String> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.load_soundfont(buf);
        }
    }

    Err(String::from("Failed to load soundfont since synthesizer not yet initialized"))
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn get_all_presets_from_sf() -> js_sys::Array {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            return x.get_all_presets_from_sf();
        } else {
            return js_sys::Array::new();
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn get_all_presets_from_sf() -> Vec<pianorhythm_proto::midi_renditions::SoundfontPreset> {
    unsafe {
        if let Some(x) = SYNTH.get() {
            x.get_all_presets_from_sf()
        } else {
            Vec::new()
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn instrument_exists(banknum: u32, prognum: u8) -> bool {
    unsafe {
        if let Some(x) = SYNTH.get() {
            x.instrument_exists(banknum, prognum)
        } else {
            false
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn add_socket(socket_id: u32, is_client: bool) -> bool {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.add_socket(&socket_id, is_client)
        } else {
            log::error!("Failed to add socket {socket_id} since synthesizer not yet initialized");
            false
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn has_socket_id(socket_id: u32) -> bool {
    unsafe {
        if let Some(x) = SYNTH.get() {
            x.has_socket_id(&socket_id)
        } else {
            false
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn remove_socket(socket_id: u32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.remove_socket(&socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn clear_all_users_except_client() {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.clear_all_users_except_client();
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_client_socket_id(socket_id: u32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_client_socket_id(&socket_id);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_max_velocity(value: Option<u8>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_max_velocity(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_min_velocity(value: Option<u8>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_min_velocity(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_use_default_instrument_when_missing_for_other_users(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_use_default_instrument_when_missing_for_other_users(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_max_note_on_time(value: Option<f64>) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_max_note_on_time(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_drum_channel_muted(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_drum_channel_muted(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_midi_output_only(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_midi_output_only(value);
        }
    }
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn get_synth_users() -> js_sys::Array {
    unsafe {
        return if let Some(x) = SYNTH.get_mut() {
            x.get_synth_users()
        } else {
            js_sys::Array::new()
        };
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn get_synth_users() -> Vec<u32> {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.get_synth_users()
        } else {
            Vec::new()
        }
    }
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn read_next() -> js_sys::Float32Array {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            let data = x.read_next();
            let buf = js_sys::Float32Array::new(&JsValue::from(2));
            buf.set_index(0, data.0);
            buf.set_index(1, data.1);
            return buf;
        } else {
            return js_sys::Float32Array::new(&JsValue::from(0));
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn read_next_rust() -> (f32, f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.read_next()
        } else {
            (0.0, 0.0)
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn equalize(sample: &mut f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.equalize(sample);
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop_lib")]
pub fn equalize_buffer(sample: &mut [f32]) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.equalize_buffer(sample);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_interpolation_method(interp_method: u32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_interpolation_method(interp_method)
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn disconnect() {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.disconnect();
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn reset() {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.reset();
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn dispose() {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.dispose();
        }

        for (_, conn) in MIDI_STATE.inputs.lock().unwrap().drain().into_iter() {
            conn.close();
        }

        for (_, conn) in MIDI_STATE.outputs.lock().unwrap().drain().into_iter() {
            conn.close();
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_octave_offset(value: i8) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_octave_offset(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_transpose_offset(value: i8) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_transpose_offset(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_max_multi_mode_channels(value: u8) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_max_multi_mode_channels(value);
        }
    }
}

pub fn set_slot_mode_raw(value: ActiveChannelsMode) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_slot_mode(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_slot_mode(value: i8) {
    set_slot_mode_raw(ActiveChannelsMode::from_i32(value as i32).unwrap_or_default());
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_primary_channel(value: u8) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_primary_channel(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_disable_velocity_for_client(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_disable_velocity_for_client(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_apply_velocity_curve(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.apply_velocity_curve = value;
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_equalizer_enabled(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.set_equalizer_enabled(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_equalizer_band(idx: usize, curve: i32, frequency: f32, resonance: f32, gain: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            let eq = x.get_equalizer_mut();
            eq.set(idx, Curve::from(curve), frequency, resonance, gain);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_equalizer_gain(idx: usize, value: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            let eq = x.get_equalizer_mut();
            eq.set_gain(idx, value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_equalizer_freq(idx: usize, value: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            let eq = x.get_equalizer_mut();
            eq.set_frequency(idx, value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_equalizer_bypass(idx: usize, value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            let eq = x.get_equalizer_mut();
            eq.set_bypass(idx, value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn set_equalizer_resonance(idx: usize, value: f32) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            let eq = x.get_equalizer_mut();
            eq.set_resonance(idx, value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn reset_equalizer() {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            let eq = x.get_equalizer_mut();
            eq.reset();
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn bypassall_equalizer(value: bool) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            let eq = x.get_equalizer_mut();
            eq.bypass_all(value);
        }
    }
}

// ---------------------
// CLIENT RELATED METHODS
// ---------------------
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn client_clear_all_audio_channels() {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.client_clear_all_audio_channels();
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn client_reset_channels_to_default() {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.client_reset_channels_to_default();
        }
    }
}

pub fn client_set_instrument_on_channel(payload: &SetChannelInstrumentPayload) {
    unsafe {
        if let Some(x) = SYNTH.get_mut() {
            x.client_set_instrument_on_channel(payload);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_get_version() -> String {
    format!("* PianoRhythm Synth v{} *", env!("CARGO_PKG_VERSION"))
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn get_synth_wasm_module() -> JsValue {
    wasm_bindgen::module()
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn get_synth_wasm_memory() -> JsValue {
    wasm_bindgen::memory()
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub async fn midi_io_start(ctx: AudioContext, number_of_channels: Option<u32>, buffer_size: Option<i32>) -> Result<AudioWorkletNode, JsValue> {
    let node = create_audio_worklet(
        ctx,
        Box::new(move |left_buf: &mut [f32], right_buf: &mut [f32]| unsafe { process_stereo(left_buf, right_buf) }),
        Box::new(move |data, socket_id, source, device_id| unsafe {
            parse_midi_data(data, socket_id, source, device_id);
        }),
        Box::new(move |midi_id, active| {
            // TODO: Uncomment once midi I/O is available in audio worklets
            // log::debug!("input {:?} | {}", midi_id, active);
            // if active {
            //     open_midi_input_connection(midi_id);
            // } else {
            //     close_midi_input_connection(midi_id);
            // }
        }),
        Box::new(move |midi_id, active| {
            // log::debug!("output {:?} | {}", midi_id, active);
            // if active {
            //     open_midi_output_connection(midi_id);
            // } else {
            //     close_midi_output_connection(midi_id);
            // }
        }),
        number_of_channels,
        buffer_size,
    )
    .await?;

    Ok(node)
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub struct Handle(Stream);

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn create_synth_stream(sample_rate: usize) -> Handle {
    let host = cpal::default_host();
    let device = host.default_output_device().expect("failed to find a default output device");

    let base_config = device.default_output_config().unwrap();
    let mut config = StreamConfig {
        sample_rate: cpal::SampleRate(sample_rate as u32),
        ..base_config.config()
    };

    Handle(match base_config.sample_format() {
        cpal::SampleFormat::F32 => run::<f32>(&device, &config.into()),
        cpal::SampleFormat::I16 => run::<i16>(&device, &config.into()),
        cpal::SampleFormat::U16 => run::<u16>(&device, &config.into()),
        // not all supported sample formats are included in this example
        _ => panic!("Unsupported sample format!"),
    })
}

#[cfg(target_arch = "wasm32")]
fn run<T>(device: &cpal::Device, config: &cpal::StreamConfig) -> Stream
where
    T: SizedSample + FromSample<f32>,
{
    let sample_rate = config.sample_rate.0 as f32;
    let num_channels = config.channels as usize;

    log::debug!("Stream config | Channels: {} | Sample Rate: {}", &num_channels, &sample_rate);

    let err_fn = |err| log::error!("an error occurred on stream: {}", err);

    let stream = device
        .build_output_stream(
            config,
            move |output: &mut [f32], _| unsafe {
                if let Some(synth) = SYNTH.get_mut() {
                    for frame in output.chunks_mut(num_channels) {
                        let (l, r) = synth.read_next();
                        let l: f32 = f32::from_sample(l);
                        let r: f32 = f32::from_sample(r);

                        let channels = [l, r];
                        for (id, sample) in frame.iter_mut().enumerate() {
                            *sample = channels[id % num_channels];
                            synth.equalize(sample);
                        }
                    }
                }
            },
            err_fn,
            None,
        )
        .unwrap();

    stream.play().unwrap();
    stream
}

#[cfg(target_arch = "wasm32")]
pub fn is_main_thread_scope() -> bool {
    js_sys::global().is_instance_of::<web_sys::Window>()
}

#[cfg(target_arch = "wasm32")]
pub fn is_worker_global_scope() -> bool {
    js_sys::global().is_instance_of::<web_sys::WorkerGlobalScope>()
}

#[cfg(target_arch = "wasm32")]
pub fn is_audio_worklet_scope() -> bool {
    js_sys::global().is_instance_of::<web_sys::AudioWorkletGlobalScope>()
}
