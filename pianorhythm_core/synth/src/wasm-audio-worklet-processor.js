/**
 * AudioWorkletProcessor for sample-accurate MIDI scheduling
 * This processor runs in the audio thread and provides precise timing
 */
class WasmProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super();
    
    // Initialize from processor options
    const [wasmModule, wasmMemory, processorPtr, bufferSize] = options.processorOptions;
    
    this.wasmModule = wasmModule;
    this.wasmMemory = wasmMemory;
    this.processorPtr = processorPtr;
    this.bufferSize = bufferSize || 128;
    
    // Scheduler state
    this.scheduledEvents = [];
    this.currentSample = 0;
    this.sampleRate = sampleRate; // Available in AudioWorkletProcessor context
    
    // Message handling
    this.port.onmessage = this.handleMessage.bind(this);
    
    // Initialize WASM processor
    this.initWasmProcessor();
  }

  initWasmProcessor() {
    try {
      // Unpack the WASM processor from the pointer
      this.wasmProcessor = this.wasmModule.WasmAudioProcessor.unpack(this.processorPtr);
      
      // Initialize the scheduler in the WASM processor
      if (this.wasmProcessor.init_scheduler) {
        this.wasmProcessor.init_scheduler(this.sampleRate);
      }
    } catch (error) {
      console.error('Failed to initialize WASM processor:', error);
    }
  }

  handleMessage(event) {
    const { type, ...data } = event.data;
    
    switch (type) {
      case 'schedule_event':
        this.scheduleEvent(data.delay_ms, data.event_data, data.event_type);
        break;
      case 'schedule_batch':
        this.scheduleBatch(data.events);
        break;
      case 'clear_events':
        this.clearEvents();
        break;
      default:
        console.warn('Unknown message type:', type);
    }
  }

  scheduleEvent(delayMs, eventData, eventType) {
    const delaySamples = Math.round((delayMs * this.sampleRate) / 1000);
    const targetSample = this.currentSample + delaySamples;
    
    const event = {
      targetSample,
      eventData: new Uint8Array(eventData),
      eventType,
      delayMs // Keep for debugging
    };

    // Insert in sorted order for efficient processing
    const insertIndex = this.findInsertIndex(targetSample);
    this.scheduledEvents.splice(insertIndex, 0, event);
  }

  scheduleBatch(events) {
    for (const event of events) {
      this.scheduleEvent(event.delay_ms, event.event_data, event.event_type);
    }
  }

  findInsertIndex(targetSample) {
    let left = 0;
    let right = this.scheduledEvents.length;
    
    while (left < right) {
      const mid = Math.floor((left + right) / 2);
      if (this.scheduledEvents[mid].targetSample <= targetSample) {
        left = mid + 1;
      } else {
        right = mid;
      }
    }
    
    return left;
  }

  clearEvents() {
    this.scheduledEvents = [];
  }

  process(inputs, outputs, parameters) {
    const output = outputs[0];
    const bufferLength = output[0].length;
    
    // Process scheduled events that are due in this buffer
    this.processScheduledEvents(bufferLength);
    
    // Process audio through WASM
    if (this.wasmProcessor && output.length >= 2) {
      try {
        // Create views into the WASM memory for audio buffers
        const leftBuffer = new Float32Array(this.wasmMemory.buffer, 
          this.wasmProcessor.get_left_buffer_ptr(), bufferLength);
        const rightBuffer = new Float32Array(this.wasmMemory.buffer, 
          this.wasmProcessor.get_right_buffer_ptr(), bufferLength);
        
        // Process stereo audio
        const success = this.wasmProcessor.process_stereo(leftBuffer, rightBuffer);
        
        if (success) {
          // Copy processed audio to outputs
          output[0].set(leftBuffer);
          if (output[1]) {
            output[1].set(rightBuffer);
          }
        }
      } catch (error) {
        console.error('WASM audio processing error:', error);
        // Fill with silence on error
        output[0].fill(0);
        if (output[1]) {
          output[1].fill(0);
        }
      }
    } else {
      // Fill with silence if no processor
      output[0].fill(0);
      if (output[1]) {
        output[1].fill(0);
      }
    }
    
    // Update sample counter
    this.currentSample += bufferLength;
    
    return true; // Keep processor alive
  }

  processScheduledEvents(bufferLength) {
    const endSample = this.currentSample + bufferLength;
    const readyEvents = [];
    
    // Collect events that are due in this buffer
    while (this.scheduledEvents.length > 0 && 
           this.scheduledEvents[0].targetSample < endSample) {
      readyEvents.push(this.scheduledEvents.shift());
    }
    
    // Execute ready events
    for (const event of readyEvents) {
      this.executeScheduledEvent(event);
    }
  }

  executeScheduledEvent(event) {
    try {
      // Schedule the event in the WASM processor for immediate execution
      if (this.wasmProcessor && this.wasmProcessor.schedule_audio_event) {
        this.wasmProcessor.schedule_audio_event(0, Array.from(event.eventData), event.eventType);
      } else {
        // Fallback: execute directly if WASM scheduling not available
        this.executeMidiEvent(event.eventData, event.eventType);
      }
    } catch (error) {
      console.error('Error executing scheduled event:', error, event);
    }
  }

  executeMidiEvent(eventData, eventType) {
    // Direct MIDI execution fallback
    // This would call into your existing MIDI processing functions
    switch (eventType) {
      case 1: // Note On
        if (eventData.length >= 11) {
          // Extract note on data and execute
          const channel = eventData[0] & 0x0F;
          const note = eventData[1];
          const velocity = eventData[2];
          const program = eventData[3];
          const bank = (eventData[4] | (eventData[5] << 8) | (eventData[6] << 16) | (eventData[7] << 24));
          const volume = eventData[8];
          const pan = eventData[9];
          const source = eventData[10];
          
          // Call WASM MIDI processing function
          if (this.wasmModule.synth_ws_socket_note_on) {
            const noteOnEvent = {
              channel,
              note,
              velocity,
              program,
              bank,
              volume,
              pan,
              source
            };
            this.wasmModule.synth_ws_socket_note_on(noteOnEvent, null);
          }
        }
        break;
        
      case 2: // Note Off
        if (eventData.length >= 4) {
          const midiData = [eventData[0], eventData[1], eventData[2]];
          const source = eventData[3];
          
          if (this.wasmModule.parse_midi_data) {
            this.wasmModule.parse_midi_data(midiData, null, source, null);
          }
        }
        break;
        
      case 3: // Sustain
      case 4: // All Sound Off
      case 5: // Pitch Bend
        if (eventData.length >= 4) {
          const midiData = [eventData[0], eventData[1], eventData[2]];
          const source = eventData[3];
          
          if (this.wasmModule.parse_midi_data) {
            this.wasmModule.parse_midi_data(midiData, null, source, null);
          }
        }
        break;
        
      default:
        console.warn('Unknown MIDI event type:', eventType);
    }
  }

  static get parameterDescriptors() {
    return [];
  }
}

// Register the processor
registerProcessor('WasmProcessor', WasmProcessor);
