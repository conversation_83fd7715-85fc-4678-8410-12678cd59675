use std::rc::Rc;

use crate::midi::audio_scheduler::schedule_midi_batch_global;
use crate::midi::{handle_ws_midi_message_optimized, BatchedNoteSchedule, HandleWebsocketMidiMessage, ScheduledNote};
use crate::reducers::app_state::AppState;
use crate::types::CoreClientApiType;
use crate::PianoRhythmWebSocketMidiPitchBend;
use pianorhythm_proto::client_message::MidiMessageOutputDto;
use pianorhythm_proto::midi_renditions::MidiDtoType;
use pianorhythm_synth::PianoRhythmWebSocketMidiNoteOn;

pub struct WasmHandleMidiMessage<'c> {
    pub core_api: &'c CoreClientApiType,
}

//impl<'c> HandleWebsocketMidiMessage for WasmHandleMidiMessage<'c> {
//    fn handle(&self, message: &MidiMessageOutputDto, state: Rc<AppState>) -> () {
//        if let Some(output) = handle_ws_midi_message(&message, state) {
//            for (ms, on_emit) in output {
//                let timeout = gloo_timers::callback::Timeout::new(ms as u32, move || on_emit());
//                timeout.forget();
//            }
//        }
//    }
//}

impl<'c> HandleWebsocketMidiMessage for WasmHandleMidiMessage<'c> {
    fn handle(&self, message: &MidiMessageOutputDto, state: Rc<AppState>) -> () {
        if let Some(batch) = handle_ws_midi_message_optimized(&message, state) {
            self.schedule_note_batch_optimized(batch);
        } else {
            #[cfg(debug_assertions)]
            log::warn!("No batch to schedule");
        }
    }

    fn schedule_note_batch_optimized(&self, batch: BatchedNoteSchedule) {
        // Use audio-context-synchronized scheduling instead of timeouts
        let notes_with_timing: Vec<(f64, ScheduledNote)> =
            batch.notes.into_iter().map(|note| (note.delay_ms, note)).collect();

        // Schedule the entire batch using sample-accurate timing
        schedule_midi_batch_global(notes_with_timing);
    }

    fn execute_note_optimized(note: ScheduledNote) {
        match note.note_data.messageType {
            MidiDtoType::NoteOn if note.note_data.has_noteOn() => {
                let value = note.note_data.get_noteOn();
                let event = PianoRhythmWebSocketMidiNoteOn {
                    channel: value.get_channel() as u8,
                    note: value.get_note() as u8,
                    velocity: value.get_velocity() as u8,
                    program: Some(value.get_program() as u8),
                    bank: Some(value.get_bank() as u32),
                    volume: Some(value.get_volume() as u8),
                    pan: Some(value.get_pan() as u8),
                    source: Some(note.note_source.to_u8()),
                    ..Default::default()
                };
                pianorhythm_synth::synth_ws_socket_note_on(event, note.socket_id_hash);
            }
            MidiDtoType::NoteOff if note.note_data.has_noteOff() => {
                let value = note.note_data.get_noteOff();
                _ = pianorhythm_synth::parse_midi_data(
                    &[
                        pianorhythm_shared::midi::NOTE_OFF_BYTE + value.get_channel() as u8,
                        value.get_note() as u8,
                        0,
                    ],
                    note.socket_id_hash,
                    Some(note.note_source.to_u8()),
                    None,
                );
            }
            MidiDtoType::Sustain if note.note_data.has_sustain() => {
                let value = note.note_data.get_sustain();
                _ = pianorhythm_synth::parse_midi_data(
                    &[
                        pianorhythm_shared::midi::CONTROLLER_BYTE,
                        64,
                        if value.value { 64 } else { 0 },
                    ],
                    note.socket_id_hash,
                    Some(note.note_source.to_u8()),
                    None,
                );
            }
            MidiDtoType::AllSoundOff => {
                if note.note_data.has_allSoundOff() {
                    _ = pianorhythm_synth::parse_midi_data(
                        &[
                            pianorhythm_shared::midi::CONTROLLER_BYTE
                                + note.note_data.get_allSoundOff().get_channel() as u8,
                            0x78,
                            0,
                        ],
                        note.socket_id_hash,
                        Some(note.note_source.to_u8()),
                        None,
                    );
                }
            }
            MidiDtoType::PitchBend => {
                if note.note_data.has_pitchBend() {
                    let value = note.note_data.get_pitchBend();
                    _ = pianorhythm_synth::synth_ws_socket_pitch(
                        PianoRhythmWebSocketMidiPitchBend {
                            channel: value.get_channel() as u8,
                            value: value.get_value(),
                        },
                        note.socket_id_hash,
                    );
                }
            }
            _ => {}
        }
    }
}
