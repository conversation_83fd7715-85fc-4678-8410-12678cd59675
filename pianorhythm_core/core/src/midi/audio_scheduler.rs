use crate::midi::ScheduledNote;
use js_sys::{Atomics, Int32Array, Object, Reflect, SharedArrayBuffer, Uint8Array};
use pianorhythm_proto::midi_renditions::MidiDtoType;
use wasm_bindgen::prelude::*;
use web_sys::AudioWorkletNode;

// Constants for the shared buffer layout
const BUFFER_SIZE: usize = 8192; // Size of the ring buffer for MIDI events
const HEADER_SIZE: usize = 4; // 4 int32 values: write_index, read_index, size, lock
const WRITE_INDEX: usize = 0;
const READ_INDEX: usize = 1;
const SIZE_INDEX: usize = 2;
const LOCK_INDEX: usize = 3;

/// Audio-context-synchronized scheduler for sample-accurate MIDI timing
pub struct AudioContextScheduler {
    audio_worklet_node: Option<AudioWorkletNode>,
    shared_buffer: Option<SharedArrayBuffer>,
    int32_view: Option<Int32Array>,
    uint8_view: Option<Uint8Array>,
}

impl AudioContextScheduler {
    pub fn new(sample_rate: f32) -> Self {
        Self {
            audio_worklet_node: None,
            shared_buffer: None,
            int32_view: None,
            uint8_view: None,
        }
    }

    pub fn new_with_buffer(sample_rate: f32, shared_buffer: SharedArrayBuffer) -> Self {
        let int32_view = Int32Array::new_with_byte_offset(&shared_buffer, 0);
        let uint8_view =
            Uint8Array::new_with_byte_offset_and_length(&shared_buffer, (HEADER_SIZE * 4) as u32, BUFFER_SIZE as u32);

        // Initialize header values
        int32_view.set_index(WRITE_INDEX as u32, 0);
        int32_view.set_index(READ_INDEX as u32, 0);
        int32_view.set_index(SIZE_INDEX as u32, 0);
        int32_view.set_index(LOCK_INDEX as u32, 0);

        Self {
            audio_worklet_node: None,
            shared_buffer: Some(shared_buffer),
            int32_view: Some(int32_view),
            uint8_view: Some(uint8_view),
        }
    }

    pub fn set_audio_worklet_node(&mut self, node: AudioWorkletNode) {
        self.audio_worklet_node = Some(node);

        // Initialize shared buffer when worklet node is set
        if self.shared_buffer.is_none() {
            self.init_shared_buffer();
        }

        // Pass the shared buffer to the audio worklet
        if let (Some(node), Some(buffer)) = (&self.audio_worklet_node, &self.shared_buffer) {
            let message = Object::new();
            Reflect::set(&message, &"type".into(), &"set_shared_buffer".into()).unwrap();
            Reflect::set(&message, &"buffer".into(), buffer).unwrap();

            if let Ok(port) = node.port() {
                let _ = port.post_message(&message);

                #[cfg(debug_assertions)]
                log::trace!("Shared buffer sent to audio worklet");
            } else {
                log::error!("Failed to get port from audio worklet node");
            }
        } else {
            log::error!("Failed to set audio worklet node because shared buffer is not initialized.");
        }
    }

    fn init_shared_buffer(&mut self) {
        // Create a shared buffer: header (4 int32s) + data buffer
        let total_size = HEADER_SIZE * 4 + BUFFER_SIZE;
        let shared_buffer = SharedArrayBuffer::new(total_size as u32);

        // Create views for the buffer
        let int32_view = Int32Array::new_with_byte_offset(&shared_buffer, 0);

        let uint8_view =
            Uint8Array::new_with_byte_offset_and_length(&shared_buffer, (HEADER_SIZE * 4) as u32, BUFFER_SIZE as u32);

        // Initialize header values
        int32_view.set_index(WRITE_INDEX as u32, 0);
        int32_view.set_index(READ_INDEX as u32, 0);
        int32_view.set_index(SIZE_INDEX as u32, 0);
        int32_view.set_index(LOCK_INDEX as u32, 0);

        self.shared_buffer = Some(shared_buffer);
        self.int32_view = Some(int32_view);
        self.uint8_view = Some(uint8_view);

        log::trace!("Shared buffer initialized for audio scheduler");
    }

    /// Schedule a MIDI event using the shared buffer
    pub fn schedule_midi_event_shared(&self, delay_ms: f64, note: &ScheduledNote) {
        if let (Some(int32_view), Some(uint8_view)) = (&self.int32_view, &self.uint8_view) {
            // Serialize the MIDI event
            let event_data = self.serialize_midi_event(note);
            let event_type = self.get_event_type(note);

            // Prepare the event data with header
            let mut buffer = Vec::<u32>::with_capacity(2 + 1 + event_data.len());

            // Convert delay_ms (f64) to two u32 values (little endian)
            let delay_bytes = delay_ms.to_le_bytes();
            let delay_low = u32::from_le_bytes([delay_bytes[0], delay_bytes[1], delay_bytes[2], delay_bytes[3]]);
            let delay_high = u32::from_le_bytes([delay_bytes[4], delay_bytes[5], delay_bytes[6], delay_bytes[7]]);

            buffer.push(delay_low);
            buffer.push(delay_high);
            buffer.push(event_type as u32);
            buffer.extend_from_slice(&event_data);
            let event_len = buffer.len() * 4; // Each u32 takes 4 bytes

            // Acquire lock using atomic operations
            while Atomics::compare_exchange(int32_view, LOCK_INDEX as u32, 0, 1).unwrap() != 0 {
                // Spin-wait until we get the lock
                Atomics::wait(int32_view, LOCK_INDEX as u32, 1).unwrap();
            }

            // --- CORRECTED BUFFER MANAGEMENT LOGIC ---

            // Atomically load the current read and write indices
            let write_idx = Atomics::load(int32_view, WRITE_INDEX as u32).unwrap() as usize;
            let read_idx = Atomics::load(int32_view, READ_INDEX as u32).unwrap() as usize;

            // Calculate the currently used space
            let used_space = (write_idx - read_idx + BUFFER_SIZE) % BUFFER_SIZE;

            // Check if there's enough free space. We leave 1 byte of slack
            // to easily distinguish between a full and an empty buffer.
            if used_space + event_len < BUFFER_SIZE {
                // Write the data to the circular buffer (convert u32 to bytes)
                for (i, &value) in buffer.iter().enumerate() {
                    let bytes = value.to_le_bytes();
                    for (j, &byte) in bytes.iter().enumerate() {
                        let idx = (write_idx + i * 4 + j) % BUFFER_SIZE;
                        uint8_view.set_index(idx as u32, byte);
                    }
                }

                // Calculate the new write index
                let new_write_idx = (write_idx + event_len) % BUFFER_SIZE;

                // **CRITICAL FIX**: Atomically store the new write index.
                // This guarantees that the AudioWorklet thread will see the change.
                Atomics::store(int32_view, WRITE_INDEX as u32, new_write_idx as i32).unwrap();

                log::trace!("MIDI event scheduled at {}ms", delay_ms);
            } else {
                log::warn!("Shared buffer full, dropping MIDI event. Used space: {}", used_space);
            }

            // Release lock
            Atomics::store(int32_view, LOCK_INDEX as u32, 0).unwrap();
            // Notify the AudioWorklet thread (or any other waiters)
            Atomics::notify(int32_view, LOCK_INDEX as u32).unwrap();
        }
    }

    /// Schedules a batch of MIDI events efficiently by locking the shared buffer only once.
    pub fn schedule_midi_batch(&self, notes: Vec<(f64, ScheduledNote)>) {
        if notes.is_empty() {
            return;
        }

        log::trace!("Scheduling batch of {} MIDI events", notes.len());

        if let (Some(int32_view), Some(uint8_view)) = (&self.int32_view, &self.uint8_view) {
            // --- 1. Serialize all events first ---
            let mut full_buffer = Vec::<u32>::new();
            for (delay_ms, note) in notes {
                let event_data = self.serialize_midi_event(&note);
                let event_type = self.get_event_type(&note);

                // Add header: delay_ms(f64) as two u32 values, event_type(u32)
                let delay_bytes = delay_ms.to_le_bytes();
                let delay_low = u32::from_le_bytes([delay_bytes[0], delay_bytes[1], delay_bytes[2], delay_bytes[3]]);
                let delay_high = u32::from_le_bytes([delay_bytes[4], delay_bytes[5], delay_bytes[6], delay_bytes[7]]);

                full_buffer.push(delay_low);
                full_buffer.push(delay_high);
                full_buffer.push(event_type as u32);
                full_buffer.extend_from_slice(&event_data);
            }
            let total_len = full_buffer.len() * 4; // Each u32 takes 4 bytes

            // --- 2. Acquire Lock (with error handling) ---
            while match Atomics::compare_exchange(int32_view, LOCK_INDEX as u32, 0, 1) {
                Ok(val) => val != 0,
                Err(e) => {
                    log::error!("Failed to compare_exchange lock: {:?}", e);
                    return; // Exit if we can't even check the lock
                }
            } {
                if let Err(e) = Atomics::wait(int32_view, LOCK_INDEX as u32, 1) {
                    log::error!("Failed to wait for lock: {:?}", e);
                    return; // Exit if waiting fails
                }
            }

            // --- 3. Write the entire batch to the buffer ---
            let write_idx = int32_view.get_index(WRITE_INDEX as u32) as usize;
            let read_idx = int32_view.get_index(READ_INDEX as u32) as usize;
            let used_space = (write_idx - read_idx + BUFFER_SIZE) % BUFFER_SIZE;

            if used_space + total_len < BUFFER_SIZE {
                // Write the data (convert u32 to bytes)
                for (i, &value) in full_buffer.iter().enumerate() {
                    let bytes = value.to_le_bytes();
                    for (j, &byte) in bytes.iter().enumerate() {
                        let idx = (write_idx + i * 4 + j) % BUFFER_SIZE;
                        uint8_view.set_index(idx as u32, byte);
                    }
                }

                // Atomically update the write index
                let new_write_idx = (write_idx + total_len) % BUFFER_SIZE;
                if let Err(e) = Atomics::store(int32_view, WRITE_INDEX as u32, new_write_idx as i32) {
                    log::error!("Failed to store new write_idx: {:?}", e);
                }
            } else {
                log::warn!("Shared buffer full, dropping MIDI batch of size {}", total_len);
            }

            // --- 4. Release Lock (with error handling) ---
            if let Err(e) = Atomics::store(int32_view, LOCK_INDEX as u32, 0) {
                log::error!("Failed to release lock: {:?}", e);
            }
            if let Err(e) = Atomics::notify(int32_view, LOCK_INDEX as u32) {
                log::error!("Failed to notify on lock release: {:?}", e);
            }
        }
    }

    /// Schedule a single MIDI event. This now calls the batch scheduler for simplicity.
    pub fn schedule_midi_event(&self, delay_ms: f64, note: ScheduledNote) {
        self.schedule_midi_batch(vec![(delay_ms, note)]);
    }

    fn serialize_midi_event(&self, note: &ScheduledNote) -> Vec<u32> {
        match note.note_data.messageType {
            MidiDtoType::NoteOn if note.note_data.has_noteOn() => {
                let value = note.note_data.get_noteOn();
                vec![
                    0x90 + value.get_channel() as u32, // Note On
                    value.get_note() as u32,
                    value.get_velocity() as u32,
                    value.get_program() as u32,
                    (value.get_bank() & 0xFF) as u32,
                    ((value.get_bank() >> 8) & 0xFF) as u32,
                    ((value.get_bank() >> 16) & 0xFF) as u32,
                    ((value.get_bank() >> 24) & 0xFF) as u32,
                    value.get_volume() as u32,
                    value.get_pan() as u32,
                    note.note_source.to_u8() as u32,
                    note.socket_id_hash.unwrap_or(0),
                ]
            }
            MidiDtoType::NoteOff if note.note_data.has_noteOff() => {
                let value = note.note_data.get_noteOff();
                vec![
                    0x80 + value.get_channel() as u32, // Note Off
                    value.get_note() as u32,
                    0, // Velocity (not used for note off)
                    note.note_source.to_u8() as u32,
                    note.socket_id_hash.unwrap_or(0),
                ]
            }
            MidiDtoType::Sustain if note.note_data.has_sustain() => {
                let channel = note.note_data.get_sustain().get_channel();
                let value = note.note_data.get_sustain();
                vec![
                    0xB0 + channel as u32, // Control Change
                    64,   // Sustain pedal
                    if value.value { 64 } else { 0 },
                    note.note_source.to_u8() as u32,
                    note.socket_id_hash.unwrap_or(0),
                ]
            }
            MidiDtoType::AllSoundOff if note.note_data.has_allSoundOff() => {
                let value = note.note_data.get_allSoundOff();
                vec![
                    0xB0 + value.get_channel() as u32, // Control Change
                    0x78,                             // All Sound Off
                    0,
                    note.note_source.to_u8() as u32,
                    note.socket_id_hash.unwrap_or(0),
                ]
            }
            MidiDtoType::PitchBend if note.note_data.has_pitchBend() => {
                let value = note.note_data.get_pitchBend();
                let pitch_value = value.get_value();
                vec![
                    0xE0 + value.get_channel() as u32,  // Pitch Bend
                    (pitch_value & 0x7F) as u32,        // LSB
                    ((pitch_value >> 7) & 0x7F) as u32, // MSB
                    note.note_source.to_u8() as u32,
                    note.socket_id_hash.unwrap_or(0),
                ]
            }
            _ => vec![], // Unknown event type
        }
    }

    fn get_event_type(&self, note: &ScheduledNote) -> u8 {
        match note.note_data.messageType {
            MidiDtoType::NoteOn => 1,
            MidiDtoType::NoteOff => 2,
            MidiDtoType::Sustain => 3,
            MidiDtoType::AllSoundOff => 4,
            MidiDtoType::PitchBend => 5,
            _ => 0,
        }
    }
}

/// Global scheduler instance
static mut AUDIO_SCHEDULER_MAIN: Option<AudioContextScheduler> = None;
static mut AUDIO_SCHEDULER_WORKER: Option<AudioContextScheduler> = None;

/// Set the AudioWorkletNode for the global scheduler
pub fn set_audio_worklet_node(node: AudioWorkletNode) {
    unsafe {
        if !crate::utils::is_main_thread_scope() {
            log::warn!("Cannot set audio worklet node on worker thread.");
            return;
        }
        
        if let Some(scheduler) = &mut AUDIO_SCHEDULER_MAIN {
            scheduler.set_audio_worklet_node(node);
            log::trace!("Audio worklet node set for audio scheduler");
        }
    }
}

/// Schedule multiple MIDI events using the global scheduler
pub fn schedule_midi_batch_global(notes: Vec<(f64, ScheduledNote)>) {
    unsafe {
        if notes.is_empty() {
            return;
        }

        if crate::utils::is_main_thread_scope() {
            if let Some(scheduler) = &AUDIO_SCHEDULER_MAIN {
                scheduler.schedule_midi_batch(notes);
            }
        } else if let Some(scheduler) = &AUDIO_SCHEDULER_WORKER {
            scheduler.schedule_midi_batch(notes);
        }
    }
}

/// (Main Thread Only) Initializes the global scheduler, creates the buffer.
/// Returns the created SharedArrayBuffer so it can be passed to other workers.
#[wasm_bindgen]
pub fn init_audio_scheduler_global(sample_rate: f32) -> SharedArrayBuffer {
    let total_size = HEADER_SIZE * 4 + BUFFER_SIZE;
    let shared_buffer = SharedArrayBuffer::new(total_size as u32);
    let scheduler = AudioContextScheduler::new_with_buffer(sample_rate, shared_buffer.clone());

    unsafe {
        AUDIO_SCHEDULER_MAIN = Some(scheduler);
    }

    log::trace!("Audio scheduler initialized on main thread.");
    shared_buffer
}

/// (Worker Thread Only) Initializes the global scheduler using an existing buffer.
#[wasm_bindgen]
pub fn init_worker_scheduler_global(sample_rate: f32, shared_buffer: SharedArrayBuffer) {
    let scheduler = AudioContextScheduler::new_with_buffer(sample_rate, shared_buffer);
    unsafe {
        AUDIO_SCHEDULER_WORKER = Some(scheduler);
    }
    log::trace!("Audio scheduler initialized on worker thread.");
}
