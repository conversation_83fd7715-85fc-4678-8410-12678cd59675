import * as core_wasm from '@core/pkg/pianorhythm_core';

/**
 * An AudioWorkletProcessor that uses a WebAssembly module to process audio.
 * It communicates with the main thread for MIDI data and uses a SharedArrayBuffer
 * for low-latency event handling.
 */
class WasmProcessor extends AudioWorkletProcessor {
    // A separate, static async method is used to handle WASM initialization.
    // The constructor of an AudioWorkletProcessor must be synchronous.
    static async initialize(options) {
        const [module, memory, handle] = options.processorOptions;
        // Wait for the WASM module to be compiled and instantiated.
        await core_wasm.default(module, memory);
        // Unpack the handle to get the processor instance.
        return core_wasm.WasmAudioProcessor.unpack(handle);
    }

    constructor(options) {
        super();
        let [module, memory, handle, _bufferSize] = options.processorOptions;
        this.crashed = false;
        this.numOfChannels = options.outputChannelCount[0] ?? 1;

        // Will be set via message
        this.sharedBuffer = null;
        this.int32View = null;
        this.uint8View = null;

        // Queue for scheduled events
        this.scheduledEvents = [];
        this.port.onmessage = (e) => {
            console.log("worklet-message", e.data);

            switch (e.data.type) {
                case "midi-data": {
                    this.processor?.parse_midi_data(e.data.data, null, null, null);
                    break;
                }

                case "set_shared_buffer": {
                    this.sharedBuffer = e.data.buffer;

                    // The first 4 * 4 = 16 bytes are for Int32Array
                    this.int32View = new Int32Array(this.sharedBuffer, 0, 4);
                    this.uint8View = new Uint8Array(this.sharedBuffer, 16); // 4 int32s = 16 bytes
                    console.log('Shared buffer received in audio worklet');
                    break;
                }

                case "web-midi-event": {
                    // { id: string, name: string, active: boolean, type: "input" | "output" }
                    let event = e.data.event;
                    if (event.type == "input") {
                        this.processor?.handle_midi_input_connection(event.name, event.active);
                    } else if (event.type == "output") {
                        this.processor?.handle_midi_output_connection(event.name, event.active);
                    }
                    console.log("web-midi-event", event);
                    break;
                }

                default:
                    console.warn("Unknown message type:", e.data.type);
            }
        };

        // Pre-allocate a small buffer for reading headers to avoid allocation in the loop.
        this.headerBuffer = new ArrayBuffer(11);
        this.headerUint8View = new Uint8Array(this.headerBuffer);

        // --- Asynchronous Initialization ---
        // We start the initialization process here but don't block the constructor.
        WasmProcessor.initialize(options)
            .then(processor => {
                this.processor = processor;
                this.port.postMessage({type: "worklet-initialized"});
                console.log("WASM AudioWorklet processor initialized successfully.");
            })
            .catch(error => {
                console.error("[Audio Worklet Initialization Error]", error);
                this.crashed = true;
                this.port.postMessage({type: "worklet-failed", error});
            });
    }

    process(_, outputs) {
        if (!this.processor || this.crashed) return true;

        const output = outputs[0];

        // Ensure we have output data
        if (!output || !output.length) return true;

        // Read from shared buffer if available
        if (this.int32View && this.uint8View) {
            this.readEventsFromSharedBuffer();
        }

        // Process all events that are due. The queue is sorted by time.
        while (this.scheduledEvents.length > 0 && this.scheduledEvents[0].scheduledTime <= currentTime) {
            const event = this.scheduledEvents.shift();
            let parsed = this.parseEventData(event.eventData, event.eventType);
            console.log("parsed", parsed, currentTime, event);

            this.processor?.parse_midi_data(
                parsed.data,
                parsed.socketHashedID,
                parsed.source,
                null,
            );
        }

        if (this.numOfChannels === 2) {
            this.processor.process_stereo(output[0], output[1]);
        } else {
            this.processor.process(output[0]);
        }

        return true;
    }

    parseEventData(eventData, eventType) {
        const socketHashedID = eventData[eventData.length - 1];
        const source = eventData[eventData.length - 2];

        if (eventType == 1) {
            // Note On
            if (eventData.length >= 11) {
                const channel = eventData[0] & 0x0F;
                const note = eventData[1];
                const velocity = eventData[2];
                const program = eventData[3];
                const bank = (eventData[4] | (eventData[5] << 8) | (eventData[6] << 16) | (eventData[7] << 24));
                const volume = eventData[8];
                const pan = eventData[9];

                return {
                    data: [eventData[0], eventData[1], eventData[2], source],
                    channel,
                    note,
                    velocity,
                    program,
                    bank,
                    volume,
                    pan,
                    source,
                    socketHashedID
                };
            }

            return null;
        } else if (eventType == 2) {
            // Note Off
            if (eventData.length >= 4) {
                const channel = eventData[0] & 0x0F;
                const note = eventData[1];
                const velocity = eventData[2];
                return {
                    data: [eventData[0], eventData[1], eventData[2], eventData[3]],
                    channel,
                    note,
                    velocity,
                    source,
                    socketHashedID
                };
            }
            return null;
        } else if (eventType == 3) {
            // Sustain
            if (eventData.length >= 4) {
                const channel = eventData[0] & 0x0F;
                const value = eventData[1];
                return {
                    data: [eventData[0], eventData[1], eventData[2], eventData[3]],
                    channel,
                    value,
                    source,
                    socketHashedID
                };
            }
            return null;
        } else if (eventType == 4) {
            // All Sound Off
            if (eventData.length >= 4) {
                const cc = eventData[0];
                const value = eventData[1];
                return {
                    cc,
                    value,
                    source,
                    socketHashedID
                };
            }
            return null;
        } else if (eventType == 5) {
            // Pitch Bend
            if (eventData.length >= 4) {
                const channel = eventData[0] & 0x0F;
                const lsb = eventData[1];
                const msb = eventData[2];
                const source = eventData[3];
                return {
                    channel,
                    lsb,
                    msb,
                    source,
                    socketHashedID
                };
            }
            return null;
        } else {
            return null;
        }
    }

    /**
     * Reads and processes events from the circular buffer in the SharedArrayBuffer.
     */
    readEventsFromSharedBuffer() {
        // This is a Single-Producer, Single-Consumer (SPSC) queue. As the only
        // consumer, we don't need to lock when reading. We only need to read
        // indices atomically.
        let readIdx = Atomics.load(this.int32View, 1); // READ_INDEX
        const writeIdx = Atomics.load(this.int32View, 0); // WRITE_INDEX
        const bufferSize = this.uint8View.length;

        // console.log("readIdx", readIdx, "writeIdx", writeIdx, "bufferSize", bufferSize);

        while (readIdx !== writeIdx) {
            console.log("readIdx", readIdx, "writeIdx", writeIdx, "bufferSize", bufferSize);
            const headerSize = 11; // 8 (delay) + 1 (type) + 2 (length)

            // Check if a full header is available to be read.
            const availableData = (writeIdx - readIdx + bufferSize) % bufferSize;
            if (availableData < headerSize) {
                break; // Not enough data for a full header.
            }

            // --- Read Event Header (Handles Buffer Wrap-Around) ---
            // CRITICAL FIX: Manually copy bytes to a local buffer to handle cases
            // where the header itself wraps around the end of the shared buffer.
            for (let i = 0; i < headerSize; i++) {
                this.headerUint8View[i] = this.uint8View[(readIdx + i) % bufferSize];
            }

            // ASSUMPTION: The producer (Rust) writes in little-endian format.
            // TypedArray views use the platform's native endianness, which is
            // little-endian on most modern systems (x86, ARM).
            const delayMs = new Float64Array(this.headerBuffer, 0, 1)[0];
            const eventType = this.headerUint8View[8];

            // --- CRITICAL FIX for Alignment Error ---
            // Manually reconstruct the u16 from two u8s (little-endian).
            // This avoids creating a Uint16Array on an unaligned offset (9).
            const dataLength = this.headerUint8View[9] | (this.headerUint8View[10] << 8);

            let currentPos = (readIdx + headerSize) % bufferSize;

            // Check if the full event payload is available.
            const availablePayload = (writeIdx - currentPos + bufferSize) % bufferSize;
            if (availablePayload < dataLength) {
                break; // The full event data has not been written yet.
            }

            // --- Read Event Data ---
            const eventData = new Uint8Array(dataLength);
            for (let i = 0; i < dataLength; i++) {
                eventData[i] = this.uint8View[(currentPos + i) % bufferSize];
            }

            // --- Queue The Event ---
            const scheduledTime = currentTime + (delayMs / 1000.0);
            const newEvent = {scheduledTime, eventType, eventData};

            // Insert into sorted queue.
            const index = this.scheduledEvents.findIndex(evt => evt.scheduledTime > scheduledTime);
            if (index === -1) {
                this.scheduledEvents.push(newEvent);
            } else {
                this.scheduledEvents.splice(index, 0, newEvent);
            }

            // --- Update Read Index ---
            readIdx = (currentPos + dataLength) % bufferSize;
            Atomics.store(this.int32View, 1, readIdx); // Atomically update READ_INDEX.
        }
    }
}

registerProcessor("WasmProcessor", WasmProcessor);